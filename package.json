{"name": "remote-hire", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ant-design/icons": "^5.2.4", "@babel/plugin-syntax-flow": "^7.23.3", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@fortawesome/fontawesome-svg-core": "^6.4.0", "@fortawesome/free-solid-svg-icons": "^6.4.0", "@fortawesome/react-fontawesome": "^0.2.0", "@heroicons/react": "^2.0.18", "@material-tailwind/react": "^2.1.2", "@monaco-editor/react": "^4.7.0", "@mui/material": "^5.14.18", "@splidejs/react-splide": "^0.7.12", "@splidejs/splide": "^4.1.4", "@tensorflow-models/blazeface": "^0.1.0", "@tensorflow/tfjs": "^4.22.0", "@types/node": "20.3.3", "@types/react": "18.2.14", "@types/react-dom": "18.2.6", "@uidotdev/usehooks": "^2.1.0", "antd": "^5.6.4", "autoprefixer": "10.4.14", "axios": "^1.6.2", "core-js": "3", "eslint": "8.44.0", "eslint-config-next": "13.4.7", "framer-motion": "^10.15.1", "html2canvas": "^1.4.1", "jquery": "^3.7.1", "jspdf": "^2.5.2", "next": "13.5.4", "odometer": "^0.4.8", "postcss": "8.4.24", "rc-progress": "^4.0.0", "rc-tween-one": "2.7.3", "react": "^18.2.0", "react-countup": "^6.4.2", "react-dom": "^18.2.0", "react-fast-marquee": "^1.6.0", "react-ga4": "^2.1.0", "react-intersection-observer": "^9.13.0", "react-odometerjs": "^3.1.0", "react-orbits": "^1.0.5", "react-phone-input-2": "^2.15.1", "react-scroll-trigger": "^0.6.14", "react-slick": "^0.29.0", "react-to-print": "^3.0.5", "sharp": "^0.32.6", "slick-carousel": "^1.8.1", "sweetalert2": "^11.14.5", "tailwindcss": "^3.3.3", "typescript": "5.1.6"}, "devDependencies": {"@types/jspdf": "^2.0.0", "@types/react-slick": "^0.23.12", "heroicons": "^2.0.18"}}