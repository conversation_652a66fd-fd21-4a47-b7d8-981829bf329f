import "../app/globals.css";

import { EyeInvisibleOutlined, EyeOutlined } from "@ant-design/icons";
import { Images, Strings } from "@/constant";
import React, { useEffect, useState } from "react";

import Image from "next/image";
import { LabelComponent } from "@/Component/label";
import { trackPageView } from "@/utils/helper";
import { useRouter } from "next/router";

const Resetpassword = () => {
  const [showpassword, setShowpassword] = useState(false);
  const [showpassword1, setShowpassword1] = useState(false);
  const [password, setpassword] = useState("");
  const [confirmpassword, setConfirmPassword] = useState("");
  const [userPassworderr, setuserPassworderr] = useState(false);
  const [userPassworder, setuserPassworder] = useState(false);
  const [passwordMismatch, setPasswordMismatch] = useState(false);
  const router = useRouter();

  const togglepasswordVisibility = () => {
    setShowpassword(!showpassword);
  };
  const togglepasswordVisibility1 = () => {
    setShowpassword1(!showpassword1);
  };
  const handleSubmit = (e: { preventDefault: () => void }) => {
    e.preventDefault();
    if (
      confirmpassword.trim() === "" ||
      password.trim() === "" ||
      password !== confirmpassword
    ) {
      setPasswordMismatch(password.trim() !== confirmpassword);
      // Set error states for both email and password
      setuserPassworder(confirmpassword.trim() === "");
      setuserPassworderr(password.trim() === "");
      return; // Prevent further execution of the function
    }
    setpassword("");
    setConfirmPassword("");
  };

  function PasswordHandle(e: {
    target: { value: React.SetStateAction<string> };
  }) {
    setpassword(e.target.value);
    if (password.trim() === "" || password.length < 6) {
      setuserPassworderr(true);
    } else {
      setuserPassworderr(false);
    }
  }

  function PasswordHandlle(e: {
    target: { value: React.SetStateAction<string> };
  }) {
    setConfirmPassword(e.target.value);
    if (confirmpassword.trim() === "" || confirmpassword.length < 6) {
      setuserPassworder(true);
    } else {
      setuserPassworder(false);
    }
  }

  useEffect(() => {
    trackPageView("/reset-password", "Reset Passoword");
  }, []);

  return (
    <div>
      <section className="bg-[#000] ">
        <div className="flex flex-col items-center justify-center px-6 py-8 mx-auto md:h-screen xs:h-[90vh] lg:py-0">
          <button
            // href="#"
            onClick={() => router.push("/")}
            className="flex items-center mb-6 text-2xl font-semibold  "
          >
            <Image
              src={Images.logo}
              alt=" logo"
              width={165}
              height={63}
              className=""
            />
          </button>
          <div className="w-full bg-white rounded-lg shadow dark:border md:mt-0 sm:max-w-md xl:p-0 dark:bg-gray-800 dark:border-gray-700 ">
            <div className="p-6 space-y-4 md:space-y-6 sm:p-8  ">
              <LabelComponent
                label={Strings.RESET_PASSWORD}
                className="font-outfit font-bold text-xl md:text-2xl"
              />
              <form
                className="space-y-4 md:space-y-6"
                action="#"
                onSubmit={handleSubmit}
              >
                <div className="relative">
                  <LabelComponent
                    label={Strings.NEW_PASSWORD}
                    className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                  />
                  <input
                    type={showpassword ? "text" : "password"}
                    value={password}
                    onChange={PasswordHandle}
                    name="password"
                    id="password"
                    placeholder="New Password"
                    required
                    className="outline-none bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                  />
                  {userPassworderr && (
                    <p className="text-red-500 font-outfit font-light text-xs mt-2">
                      {Strings.PASSWORD_MUST_BE}
                    </p>
                  )}

                  <span
                    className="absolute right-3 top-[50px] text-lg transform -translate-y-1/2 cursor-pointer flex items-center"
                    onClick={togglepasswordVisibility}
                  >
                    {showpassword ? <EyeInvisibleOutlined /> : <EyeOutlined />}
                  </span>
                </div>
                <div className="relative">
                  <LabelComponent
                    label={Strings.CONFIRM_NEW_PASSWORD}
                    className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                  />
                  <input
                    type={showpassword1 ? "text" : "password"}
                    value={confirmpassword}
                    onChange={PasswordHandlle}
                    name="password"
                    id="password"
                    placeholder="Confirm New Password"
                    required
                    className="outline-none bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                  />
                  {userPassworder && (
                    <p className="text-red-500 font-outfit font-light text-xs mt-2">
                      {Strings.PASSWORD_MUST_BE}
                    </p>
                  )}

                  <span
                    className="absolute right-3 top-[50px] text-lg transform -translate-y-1/2 cursor-pointer flex items-center"
                    onClick={togglepasswordVisibility1}
                  >
                    {showpassword1 ? <EyeInvisibleOutlined /> : <EyeOutlined />}
                  </span>
                </div>
                {passwordMismatch && (
                  <p className="text-red-500 font-outfit font-light text-sm mt-2">
                    {Strings.PASSWORDS_DO_NOT_MATCH}
                  </p>
                )}
                <button
                  type="submit"
                  className="w-full text-white bg-gradient-to-l from-tosca to-contessa font-outfit font-medium text-lg rounded-full py-3 text-center"
                >
                  {Strings.SUBMIT}
                </button>
              </form>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Resetpassword;
