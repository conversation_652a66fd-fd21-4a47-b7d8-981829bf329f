import "../app/globals.css";

import { Images, Strings } from "@/constant";
import { useEffect, useState } from "react";

import Image from "next/image";
import { LabelComponent } from "@/Component/label";
import Link from "next/link";
import { trackPageView } from "@/utils/helper";
import { useRouter } from "next/router";

const Forgotpassword = () => {
  const [password, setpassword] = useState("");
  const [email, setEmail] = useState("");
  const [emailerr, setEmailerr] = useState(false);
  const [userPassworderr, setuserPassworderr] = useState(false);
  const [emailInvalidError, setEmailInvalidError] = useState(false);
  const router = useRouter();

  const handleSubmit = (e: { preventDefault: () => void }) => {
    e.preventDefault();
    if (email.trim() === "" || password.trim() === "") {
      // Set error states for both email and password
      setEmailerr(email.trim() === "");
      setuserPassworderr(password.trim() === "");
      return; // Prevent further execution of the function
    }
    setEmailInvalidError(false);
  };
  const validateEmail = (email: any) => {
    // Regular expression for basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  function EmailHandle(e: { target: { value: any } }) {
    const inputEmail = e.target.value;
    setEmail(inputEmail);

    // Check for empty email and invalid email format
    if (inputEmail.trim() === "") {
      setEmailerr(true);
      setEmailInvalidError(false); // Hide emailInvalidError when email is empty
    } else if (!validateEmail(inputEmail)) {
      setEmailerr(false); // Hide emailerr when email is invalid
      setEmailInvalidError(true);
    } else {
      // Email is not empty and valid
      setEmailerr(false);
      setEmailInvalidError(false);
    }
  }
  useEffect(() => {
    trackPageView("/forgot-password", "Forgot-Passowrd");
  }, []);

  return (
    <div>
      <section className="bg-[#000] ">
        <div className="flex flex-col items-center justify-center px-6 py-8 mx-auto md:h-screen xs:h-[90vh] lg:py-0">
          <button
            onClick={() => router.push("/")}
            className="flex items-center mb-6 text-2xl font-semibold  "
          >
            <Image
              src={Images.logo}
              alt="logo"
              width={165}
              height={63}
              className=""
            />
          </button>
          <div className="w-full bg-white rounded-lg shadow dark:border md:mt-0 sm:max-w-md xl:p-0 dark:bg-gray-800 dark:border-gray-700 ">
            <div className="p-6 space-y-4 md:space-y-6 sm:p-8  ">
              <LabelComponent
                label={Strings.FORGOT_PASSWORD}
                className="text-xl font-outfit font-bold md:text-2xl"
              />
              <LabelComponent
                label={Strings.ENTER_YOUR_EMAIL}
                className="text-sm font-light font-outfit text-gray-500 dark:text-gray-400"
              />
              <form
                className="space-y-4 md:space-y-6"
                action="#"
                onSubmit={handleSubmit}
              >
                <div>
                  <LabelComponent
                    label={Strings.YOUR_EMAIL}
                    className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                  />
                  <input
                    type="email"
                    name="email"
                    id="email"
                    value={email}
                    onChange={EmailHandle}
                    className="outline-none bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-600 dark:focus:border-blue-600"
                    placeholder="<EMAIL>"
                    required
                  />
                  {emailInvalidError && (
                    <p className="text-red-500 font-outfit font-light text-xs mt-2">
                      {Strings.Email_is_invalid}
                    </p>
                  )}
                  {!emailInvalidError && emailerr && (
                    <p className="text-red-500 font-outfit font-light text-xs mt-2">
                      {Strings.Email_is_Required}
                    </p>
                  )}
                </div>
                <Link href={"/reset-password"} className="mt-7 flex">
                  <button
                    type="submit"
                    className="w-full text-white bg-gradient-to-l from-tosca to-contessa hover:bg-gradient-to-l hover:from-contessa hover:to-tosca
                     font-outfit font-medium text-lg rounded-full py-3 text-center"
                  >
                    {Strings.RESET_PASSWORD}
                  </button>
                </Link>
                <div className="flex items-center justify-end">
                  <Link
                    href="/login"
                    className="text-sm font-medium text-primary-600 hover:underline dark:text-primary-500 mb-5"
                  >
                    {Strings.Back_to_login}
                  </Link>
                </div>
              </form>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Forgotpassword;
