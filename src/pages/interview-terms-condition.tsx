import "../app/globals.css";

import React, { useEffect, useState } from "react";

import Cookie from "@/Component/cookies";
import { Footer } from "@/Component/footer";
import { LabelComponent } from "@/Component/label";
import NavbarComponent from "@/Component/Navbar";
import { trackPageView } from "@/utils/helper";
import Loader from "@/Component/loader";

const Termscondition = () => {
    useEffect(() => {
        trackPageView("/terms-condition", "Terms-Condition");
    }, []);

    const sendEmail = () => {
        window.location.href = "mailto:<EMAIL>?subject=Inquiry&body=Hello, I have a question regarding the AI interview.";
    };

    return (
        <div className="scroll-smooth">
            <div className="">
                <Cookie />
            </div>
            <div>
                <NavbarComponent />
            </div>
            <section>
                <div className=" xl:mx-[250px]  lg:mx-[150px] md:mx-[20px] xs:mx-[18px] ">
                    <div className="text-center  my-[100px]">
                        <LabelComponent
                            label="Terms & Conditions"
                            className="font-outfit font-bold xs:text-4xl md:text-6xl text-white"
                        />
                    </div>
                    <div className="xs:mx-7  space-y-2 ">
                        <LabelComponent
                            label="Terms And Conditions"
                            className="uppercase font-outfit font-semibold text-[28px] text-white"
                        />
                        <p className="font-outfit font-medium text-[20px] text-white">
                            Last updated November 22, 2023
                        </p>
                    </div>
                    <div className="xs:mx-7  mt-16 space-y-4 font-outfit">
                        <h1 className="font-outfit text-[22px] font-bold text-white">
                            TABLE OF CONTENTS
                        </h1>
                        <ul className="underline flex flex-col font-outfit space-y-1 text-white">
                            <li>
                                {" "}
                                <a href="#1">1. PURPOSE OF DATA COLLECTION</a>
                            </li>
                            <li>
                                <a href="#2">2. CONSEQUENCES OF VIOLATIONS</a>
                            </li>
                            <li>
                                {" "}
                                <a href="#3">3. CONSENT</a>
                            </li>
                        </ul>
                    </div>
                    <div id="1" className="mt-[80px]">
                        <ul className="">
                            <li className=" text-[20px] font-bold text-white">
                                1.PURPOSE OF DATA COLLECTION
                            </li>
                        </ul>
                        <p className="mb-[10px] text-[16px] font-outfit tracking-wide text-white	">
                            We utilize advanced AI-driven methods to ensure the integrity and authenticity of the interview process. To achieve this, we may collect and process the following information:

                            Audio Verification: Your voice will be analyzed to verify your authenticity and detect any potential fraudulent activity.

                            Video Monitoring: Your video feed will be recorded and monitored to ensure that you are physically present and not engaging in any dishonest practices.

                            Screen Sharing: You may be required to share your entire screen to confirm that no unauthorized tools, browsers, or third-party websites are being used during the interview.
                        </p>
                    </div>
                    <div id="2" className="mt-[80px]">
                        <ul className="">
                            <li className=" text-[20px] font-bold text-white ">
                                2. CONSEQUENCES OF VIOLATIONS
                            </li>
                        </ul>
                        <p className="mb-[10px] text-[16px] font-outfit tracking-wide text-white	">
                            Failure to comply with these terms may result in:

                            Immediate termination of the interview session.
                            Disqualification from the recruitment process.
                            Legal action in cases of severe fraudulent behavior.
                        </p>
                    </div>
                    <div id="3" className="mt-[80px]">
                        <ul className="">
                            <li className=" text-[20px] font-bold text-white">
                                3.CONSENT
                            </li>
                        </ul>
                        <p className="mb-[10px] text-[16px] font-outfit tracking-wide text-white	">

                            By participating in the AI interview, you acknowledge and agree to the collection and use of your audio, video, and screen-sharing data as outlined in these terms. You also confirm that you are providing this information voluntarily and with full understanding of its purpose.
                        </p>
                        <p className="mt-6">
                            We reserve the right to update or modify these terms at any time without prior notice. Any changes will be effective immediately upon posting.
                        </p>
                        <p className="mt-6">
                            For any questions or concerns regarding these terms, please <a href="mailto:<EMAIL>" className="text-blue-600 cursor-pointer"> <EMAIL></a>.
                        </p>
                        <p className="mt-6 mb-9">
                            By proceeding with the AI interview, you agree to abide by these terms and conditions.
                        </p>
                    </div>
                </div>
            </section>
            <footer>
                <Footer />
            </footer>
        </div>
    );
};

export default Termscondition;
