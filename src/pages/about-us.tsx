"use client";

import "../app/globals.css";

import Hiretopengineer, { toggleModal } from "@/Component/Hiretopengineer";
import React, { useEffect, useState } from "react";

import AboutCountComponent from "@/Component/Countcomponent-about";
import CardComponent from "@/Component/card-component";
import { Footer } from "@/Component/footer";
import { GetServerSideProps } from "next";
import { LabelComponent } from "@/Component/label";
import NavbarComponent from "@/Component/Navbar";
import { Strings } from "@/constant";
import axios from "axios";
import { motion } from "framer-motion";
import { trackPageView } from "@/utils/helper";
import { useInView } from "react-intersection-observer";

interface FounderDataItem {
  designation: string | undefined;
  lastName: string;
  firstName: string;
  profilePicture: string;
  fullName: string;
  position: string;
}
interface DataItem {
  fullName: string | undefined;
  designation: string | undefined;
  profilePicture: string;
  firstName: string;
  lastName: String;
  position: string;
}

interface AboutUsPageProps {
  teamMembers: DataItem[];
  founders: DataItem[];
}

export const getServerSideProps: GetServerSideProps = async () => {
  try {
    const config = {
      method: "get",
      maxBodyLength: Infinity,
      url: `${process.env.NEXT_PUBLIC_API_BASE_URL}home/getTeamDetails`,
      headers: {},
    };

    const response = await axios.request(config);

    const teamMembers = response?.data?.teamDetailsData?.teamMembers || [];
    const founders = response?.data?.teamDetailsData?.Founders || [];

    return {
      props: {
        teamMembers,
        founders,
      },
    };
  } catch (error) {
    console.error("Error fetching team details:", error);
    return {
      props: {
        teamMembers: [],
        founders: [],
      },
    };
  }
};

const Aboutus: React.FC<AboutUsPageProps> = ({ teamMembers, founders }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isAnimated, setIsAnimated] = useState(false);
  const [isSmallScreen, setIsSmallScreen] = useState(false);

  const { ref, inView } = useInView({
    triggerOnce: true,
  });

  React.useEffect(() => {
    if (inView) {
      setIsAnimated(true);
    }
  }, [inView]);

  const handleButtonClick = () => {
    toggleModal(isOpen, setIsOpen);
  };

  useEffect(() => {
    trackPageView("/about-us", "About Us Page");
  }, []);

  const checkScreenSize = () => {
    setIsSmallScreen(window.innerWidth <= 768);
  };

  useEffect(() => {
    checkScreenSize();

    window.addEventListener("resize", checkScreenSize);

    return () => window.removeEventListener("resize", checkScreenSize);
  }, []);

  const storyTitle2 = {
    hideBottom: {
      opacity: 0,

      y: isSmallScreen ? 100 : 200,
    },
    showBottom: {
      opacity: 1,
      y: 0,
      transition: {
        ease: "easeInOut",
        duration: 1,
      },
    },
    exit: {
      opacity: 0,
      y: isSmallScreen ? -100 : -200,
      transition: {
        ease: "easeInOut",
        duration: 0.5,
      },
    },
  };

  return (
    <div className="bg-black ">
      <div>
        <NavbarComponent />
      </div>
      <div className=" xs:mt-[80px] md:my-[100px] ">
        <LabelComponent
          className=" text-transparent bg-clip-text bg-gradient-to-t from-contessa to-tosca font-bold xs:text-4xl lg:text-5xl flex justify-center animation-element ${inView ? 'in-view' : ''"
          label={Strings.ABOUT_US}
        />
        <motion.div
          initial="hideBottom"
          whileInView="showBottom"
          exit="exit"
          variants={storyTitle2}
          ref={ref}
          animate={isAnimated ? "showBottom" : "hideBottom"}
          className="flex justify-center"
        >
          <div className="   xs:w-[320px] md:w-[600px] lg:w-[700px] text-center">
            <div className="text-white  flex justify-center font-normal xs:text-lg lg:text-xl text-center mt-5 mx-2">
              {Strings.AT_EREMOTEHIRE}
            </div>
            <div className="text-white  flex justify-center font-normal xs:text-lg lg:text-xl text-center my-5 mx-2">
              {Strings.WE_ALSO_HAVE}
            </div>
            <div className="text-white  flex justify-center font-normal xs:text-lg lg:text-xl text-center mx-2">
              {Strings.WORK_IS_NOW}
            </div>
          </div>
        </motion.div>
      </div>
      <div className="xs:mt-[7rem] md:mt-[170px] lg:mt-[13rem] bg-yellow-900- max-w-screen-2xl m-auto">
        <motion.div
          initial="hideBottom"
          whileInView="showBottom"
          exit="exit"
          variants={storyTitle2}
          ref={ref}
          animate={isAnimated ? "showBottom" : "hideBottom"}
        >
          <AboutCountComponent />
        </motion.div>
      </div>
      <div>
        {/* {founders?.length > 0 ? (
            <>
              <motion.div
                initial="hideBottom"
                whileInView="showBottom"
                exit="exit"
                variants={storyTitle2}
                ref={ref}
                animate={isAnimated ? "showBottom" : "hideBottom"}
              >
                <LabelComponent
                  label={Strings.MEET_THE_FOUNDER}
                  className=" bg-pink-800-  text-transparent bg-clip-text bg-gradient-to-r from-tosca to-contessa font-bold xs:text-4xl lg:text-5xl flex justify-center my-20 "
                />
              </motion.div>
              <motion.div
                initial="hideBottom"
                whileInView="showBottom"
                exit="exit"
                variants={storyTitle2}
                ref={ref}
                animate={isAnimated ? "showBottom" : "hideBottom"}
              >
                {(founders as FounderDataItem[]).map((item, index) => (
                  <div className="flex justify-center" key={index}>
                    <div className="border-transparent rounded-[40px] overflow-hidden xs:w-[18.75rem] lg:w-[25rem] bg-gradient-to-r from-CodGray to-contessa ">
                      <div className=" border-transparent rounded-l-[40px] bg-gradient-to-r from-tosca to-contessa xs:w-[18.75rem] lg:w-[25rem] flex justify-center">
                        <div className="z-50 my-10 rounded-full ">
                          <Image
                            src={item?.profilePicture}
                            alt="Awtar"
                            height={700}
                            width={700}
                            className="h-44 w-44 rounded-full"
                          />
                        </div>
                      </div>
                      <div className="border-transparent rounded-tr-[40px]  bg-CodGray xs:w-[18.80rem] lg:w-[25rem] h-[100px] flex justify-center items-center ">
                        <div className="mx-2 text-center ">
                          <LabelComponent
                            className="text-3xl  hidden"
                            label={item?.firstName + item?.lastName}
                          />

                          <LabelComponent
                            label={item?.designation}
                            className="text-white  font-semibold xs:text-xl lg:text-3xl mt-7"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </motion.div>
            </>
          ) : (
            <div className="text-center"></div>
          )} */}

        {teamMembers?.length > 0 ? (
          <>
            <motion.div
              initial="hideBottom"
              whileInView="showBottom"
              exit="exit"
              variants={storyTitle2}
              ref={ref}
              animate={isAnimated ? "showBottom" : "hideBottom"}
            >
              <LabelComponent
                label={Strings.OUR_INCREDIBLE_TEAM}
                className=" font-bold xs:text-3xl lg:text-5xl text-transparent bg-clip-text bg-gradient-to-l from-contessa to-tosca flex justify-center md:my-20 xs:my-10"
              />
            </motion.div>
            <div className="max-w-screen-2xl m-auto">
              <div className="flex flex-wrap justify-center sm:gap-x-6 lg:gap-x-8">
                {teamMembers?.slice(0,8)?.map((data: DataItem, index: number) => (
                  <motion.div
                    key={index}
                    initial="hideBottom"
                    whileInView="showBottom"
                    exit="exit"
                    variants={storyTitle2}
                    ref={ref}
                    animate={isAnimated ? "showBottom" : "hideBottom"}
                  >
                    <CardComponent
                      key={index}
                      imageSrc={data?.profilePicture}
                      name={data?.firstName + " " + data?.lastName}
                      title={data?.designation}
                    />
                  </motion.div>
                ))}
              </div>
            </div>
          </>
        ) : (
          <div className="text-center"></div>
        )}
      </div>
      <motion.div
        initial="hideBottom"
        whileInView="showBottom"
        exit="exit"
        variants={storyTitle2}
        ref={ref}
        animate={isAnimated ? "showBottom" : "hideBottom"}
      >
        <LabelComponent
          label={Strings.AND_MORE}
          className=" font-bold text-4xl text-transparent bg-clip-text bg-gradient-to-l from-contessa to-tosca flex justify-center xs:mt-10 xs:mb-14 md:mb-14 md:mt-10"
        />
      </motion.div>
      <motion.div
        initial="hideBottom"
        whileInView="showBottom"
        exit="exit"
        variants={storyTitle2}
        ref={ref}
        animate={isAnimated ? "showBottom" : "hideBottom"}
        className="flex justify-center mb-20"
      >
        <div className="rounded-3xl md:rounded-[30px] flex justify-center mx-5 lg:mx-20 w-full max-w-screen-2xl m-auto bg-gradient-to-r from-tundora to-CodGray py-7 px-2 md:p-10">
          <div className="text-center">
            <h1 className="text-white font-bold text-xl md:text-4xl ">
              {Strings.HIRE_MANAGE}
            </h1>
            <h1 className="text-white font-extralight text-base md:text-2xl flex justify-center my-5 lg:my-10">
              {Strings.GET_A_FULLY_REMOTE}
            </h1>
            <button
              onClick={handleButtonClick}
              className="rounded-full bg-gradient-to-t  from-contessa to-tosca  hover:bg-gradient-to-b hover:from-Contessa hover:to-Tosca py-3 md:py-5 px-8 md:px-10 transition-transform hover:scale-110 transform duration-700"
            >
              <h1 className="font-light text-base md:text-2xl text-white">
                {Strings.HIRE_A_TOP_ENGINEER}
              </h1>
            </button>
          </div>
          <Hiretopengineer isOpen={isOpen} setIsOpen={setIsOpen} />
        </div>
      </motion.div>
      <footer>
        <Footer />
      </footer>
    </div>
  );
};

export default Aboutus;
