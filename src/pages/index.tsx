import HomeScreen from "../Component/HomePage/HomeScreen";
import axios from "axios";

export async function getServerSideProps() {
  try {
    const [
      logoResponse,
      reviewResponse,
      availbleHireDeveloper,
      communityResponse,
    ] = await Promise.all([
      axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}home/getTrustedByLogoData`
      ),
      axios.get(`${process.env.NEXT_PUBLIC_API_BASE_URL}home/getClientReview`),
      axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}home/getAvailableHireDeveloper`
      ),
      axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}home/getCommunityDetails`
      ),
    ]);

    const logos = logoResponse?.data?.logoData;
    const reviews = reviewResponse?.data?.clientReviewData;
    const openToWork = availbleHireDeveloper?.data?.developerData?.openToWork;
    const communityDetails = communityResponse?.data?.communityDetailsData;

    return {
      props: {
        logos,
        reviews,
        openToWork,
        communityDetails,
      },
    };
  } catch (error) {
    console.error("Failed to fetch data:", error);
    return {
      props: {
        logos: [],
        reviews: [],
        openToWork: [],
        communityDetails: [],
      },
    };
  }
}

export default function Home({
  reviews,
  logos,
  openToWork,
  communityDetails,
}: any) {
  return (
    <div>
      <HomeScreen
        resources={reviews}
        logos={logos}
        openToWork={openToWork}
        communityDetails={communityDetails}
      />
    </div>
  );
}
