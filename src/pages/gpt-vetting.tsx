import React, { useEffect, useRef, useState } from "react";

import { Footer } from "@/Component/footer";
import GptVettingCard from "@/Component/GptVettingCard";
import ImageSliderComponent from "@/Component/HomePage/ImageSlider";
import Link from "next/link";
import NavbarComponent from "@/Component/Navbar";
import axios from "axios";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";

export async function getServerSideProps() {
  try {
    const [logoResponse] = await Promise.all([
      axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}home/getTrustedByLogoData`
      ),
    ]);

    const logos = logoResponse?.data?.logoData;

    return {
      props: {
        logos,
      },
    };
  } catch (error) {
    console.error("Failed to fetch data:", error);
    return {
      props: {
        logos: [],
      },
    };
  }
}
const GptVetting = ({ logos }: any) => {
  const [showControls, setShowControls] = useState(false);
  const [showPlayIcon, setShowPlayIcon] = useState(false);
  const [cursorPosition, setCursorPosition] = useState({ x: 0, y: 0 });
  const sectionRef = useRef<HTMLDivElement>(null);
  const [isAnimated, setIsAnimated] = useState(false);
  const [isSmallScreen, setIsSmallScreen] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const videoRef = useRef<HTMLVideoElement | null>(null);
  const { ref, inView } = useInView({
    triggerOnce: true,
  });

  React.useEffect(() => {
    if (inView) {
      setIsAnimated(true);
    }
  }, [inView]);

  const handleVideoClick = () => {
    if (videoRef.current) {
      if (videoRef.current.paused) {
        videoRef.current.play();
        setIsPlaying(true);
        setShowPlayIcon(false);
        setTimeout(() => {
          setShowControls(true);
        }, 500);
      } else {
        videoRef.current.pause();
        setIsPlaying(false);
        setShowControls(false);
        setShowPlayIcon(true);
      }
    }
  };

  const handleVideoEnd = () => {
    setIsPlaying(false);
    setShowControls(false);
    setShowPlayIcon(true);
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (sectionRef.current) {
      const rect = sectionRef.current.getBoundingClientRect();
      setCursorPosition({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top,
      });
    }
  };

  const data = [
    {
      image: "/images/gpt.svg",
      text: "The resume does not provide sufficient data to determine whether to interview a candidate",
    },
    {
      image: "/images/communication.png",
      text: "Recruitment cycles entailing excessive interview rounds waste time and money",
    },
  ];

  const checkScreenSize = () => {
    setIsSmallScreen(window.innerWidth <= 768);
  };

  useEffect(() => {
    checkScreenSize();

    window.addEventListener("resize", checkScreenSize);

    return () => window.removeEventListener("resize", checkScreenSize);
  }, []);

  const variants = {
    fromLeft: {
      hidden: { opacity: 0, x: -100 },
      visible: { opacity: 1, x: 0, transition: { duration: 2 } },
    },
    fromRight: {
      hidden: { opacity: 0, x: 100 },
      visible: { opacity: 1, x: 0, transition: { duration: 2 } },
    },
  };
  return (
    <div className="max-w-screen-2xl m-auto">
      <NavbarComponent />
      <section>
        <div className="text-center my-20 md:my-[200px]">
          <h1 className="font-semibold xs:text-4xl md:text-[4rem] text-white ">
            <span className="text-transparent bg-clip-text bg-gradient-to-l from-contessa to-tosca">
              eRemoteHire
            </span>{" "}
            to vet
            <p className="md:mt-10">global talent</p>
          </h1>
          <div className="mt-16 flex justify-center items-center gap-x-4">
            <Link
              href={process.env.NEXT_PUBLIC_CLIENT_REGISTER_URL || "#"}
              target="_blank"
            >
              <button className="rounded-lg text-white bg-gradient-to-tl from-contessa to-tosca hover:bg-gradient-to-br hover:from-Contessa hover:to-Tosca p-[1px] transition-transform ">
                <div className="text-center bg-[linear-gradient(to_top,_#241415,_black)] h-full w-full rounded-lg py-3 px-6">
                  Create an account
                </div>
              </button>
            </Link>
            <Link href="https://calendly.com/dharmendra-eremotehire/gpt-vetting-demo">
              <button className="rounded-lg text-white bg-gradient-to-tl from-contessa to-tosca  hover:bg-gradient-to-br hover:from-Contessa hover:to-Tosca py-3 px-6 transition-transform">
                Book a demo
              </button>
            </Link>
          </div>
          {/* <Link href="/start-vetting">
            <button className="mt-16 rounded-lg bg-gradient-to-tl from-contessa to-tosca  hover:bg-gradient-to-br hover:from-Contessa hover:to-Tosca py-3 px-6 transition-transform hover:scale-110 transform duration-300">
              Get Started
            </button>
          </Link> */}
        </div>
      </section>
      {/* <section className="mx-5 lg:mx-28">
        <div
          ref={sectionRef}
          onMouseEnter={() => setShowPlayIcon(true)}
          onMouseLeave={() => setShowPlayIcon(false)}
          onMouseMove={handleMouseMove}
          className="bg-gradient-to-l from-[#481e1f] to-[#6b443a] p-[2px] lg:p-1 rounded-xl relative overflow-hidden border-clockwise"
        >
          <div className="bg-black p-2 lg:p-4 rounded-xl">
            <video
              ref={videoRef}
              className={`object-cover rounded-xl ${
                isPlaying ? "opacity-100" : "opacity-50"
              }`}
              onClick={handleVideoClick}
              controls={showControls}
              onEnded={handleVideoEnd}
            >
              <source src={Images.videomp4} type="video/mp4" />
            </video>
          </div>
          {showPlayIcon && !isPlaying && (
            <Image
              src={Images.Play_icon}
              alt="Play Icon"
              height={500}
              width={500}
              className="absolute pointer-events-none xs:hidden lg:flex h-24 w-24 bg-gradient-to-l from-[#481e1f] to-[#6b443a] rounded-full transition-all duration-700 ease-out"
              style={{
                left: `${cursorPosition.x}px`,
                top: `${cursorPosition.y}px`,
                transform: "translate(-50%, -50%)",
              }}
            />
          )}
        </div>
      </section> */}
      {/* <section>
        <div className="text-center my-20 md:my-32 mx-5 lg:mx-[30%]">
          <h1 className="font-normal xs:text-2xl md:text-4xl text-white ">
            Our mission is to make every recruiter on the planet 100x more
            powerful
          </h1>
        </div>
      </section> */}
      <div className="w-full py-[2px] bg-gradient-to-tr from-[#481e1f] to-[#6b443a]">
        <div className="w-full py-20 bg-black flex justify-center items-center">
          <ImageSliderComponent logos={logos} />
        </div>
      </div>
      <section>
        <div className="text-center my-20 md:my-32 mx-5 lg:mx-[30%]">
          <h1 className="font-normal xs:text-2xl md:text-4xl text-white ">
            Most initial screening calls are a waste of time & money
          </h1>
        </div>
      </section>
      <div className="flex flex-wrap justify-center gap-6 mb-20 lg:mb-40 mx-5 lg:mx-0 xs:overflow-hidden">
        {data.map((item, index) => (
          <motion.div
            key={index}
            initial="hidden"
            whileInView="visible"
            exit="exit"
            variants={index % 2 === 0 ? variants.fromLeft : variants.fromRight}
            viewport={{ once: true }}
            ref={ref}
            animate={isAnimated ? "showBottom" : "hideBottom"}
          >
            <GptVettingCard key={index} image={item.image} text={item.text} />
          </motion.div>
        ))}
      </div>
      <Footer />
    </div>
  );
};

export default GptVetting;
