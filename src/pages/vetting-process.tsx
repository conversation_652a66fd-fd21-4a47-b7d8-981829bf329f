import "../app/globals.css";

import Hiretopengineer, { toggleModal } from "@/Component/Hiretopengineer";
import { Images, Strings } from "@/constant";
import { useEffect, useState } from "react";

import <PERSON>ie from "@/Component/cookies";
import { Footer } from "@/Component/footer";
import Image from "next/image";
import { LabelComponent } from "@/Component/label";
import Loader from "@/Component/loader";
import NavbarComponent from "@/Component/Navbar";
import React from "react";
import { trackPageView } from "@/utils/helper";

const HowWeHire = () => {
  const [isOpen, setIsOpen] = useState(false);

  const handleButtonClick = () => {
    toggleModal(isOpen, setIsOpen);
  };
  useEffect(() => {
    trackPageView("/vetting-process", "Vetting-Process");
  }, []);

  const imagesAndLabels = [
    {
      id: 0,
      image: Images.Setting,
      label: "Loom Video",
      label_second:
        "First, the engineers apply online by attaching a 3 minute loom video of themselves going through their resume. Here we can quickly do most of the filtering by assessing experience, resume, and communication. ",
      filledIcon: Images.Setting_fill,
    },
    {
      id: 1,
      image: Images.Busines,
      label: "GPT-vetting",
      label_second:
        "A GPT-4 powered test deeply pre-screens the technical knowledge of all candidates.",
      filledIcon: Images.Busines_fill,
    },
    {
      id: 2,
      image: Images.Desktop,
      label: "1-2 Technical interviews",
      label_second:
        "We then conduct 1-2 technical rounds by senior engineers in our core team.",
      filledIcon: Images.Desktop_fill,
    },
    {
      id: 3,
      image: Images.Msg,
      label: "Casual Interview",
      label_second:
        "After the technicals, we conduct a casual interview to deeply assess their soft skills: communication, passion, and attitude  ",
      filledIcon: Images.Msg_fill,
    },
    {
      id: 3,
      image: Images.Brain,
      label: "Training",
      label_second:
        "After they've passed our vetting process, we take each and every engineer through our 'AI-Powered Engineer' training to teach them how to use github copilot, chatGPT, copilot labs, and more. This makes each engineer approximately 2x more efficient. ",
      filledIcon: Images.Brain_fill,
    },
  ];
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentImageIndex(
        (prevIndex) => (prevIndex + 1) % imagesAndLabels.length
      );
    }, 2000);
    return () => {
      clearInterval(timer);
    };
  });

  const imagesAndLabelsTwo = [
    {
      id: 0,
      image: Images.USER,
      label: "Top 1% Certified Engineer ",
      filledIcon: Images.USER,
    },
  ];

  return (
    <div className="max-w-screen-2xl m-auto">
      <div className="">
        <Cookie />
      </div>
      <div>
        <NavbarComponent />
      </div>
      <div className=" bg-red-900- md:my-[100px] xs:my-[50px] flex flex-col items-center justify-center w-full  ">
        <LabelComponent
          label={Strings.OUR_ROBUST_VETTING_PROCESS}
          className="text-transparent bg-gradient-to-r from-tosca to-contessa font-outfit font-bold md:text-[50px] text-2xl bg-clip-text"
        />
        <div className="flex flex-col  items-center justify-center mt-10">
          <p className="text-center px-6 text-xs md:text-[16px] lg:text-lg font-outfit  font-extralight justify-center flex  text-white">
            Every engineer that joins our talent pool goes through our
            GPT-vetting, 2-3 manual interviews, and takes an AI-Powered
            <br></br>
            engineer course
          </p>
        </div>
      </div>
      <div className="text-center">
        <button
          onClick={handleButtonClick}
          className="  rounded-full bg-gradient-to-t from-contessa to-tosca  hover:bg-gradient-to-b hover:from-Contessa hover:to-Tosca py-5 px-10 transition-transform hover:scale-110 transform duration-700"
        >
          <LabelComponent
            label={Strings.HIRE_A_TOP_ENGINEER}
            className="font-outfit font-light xs:text-xl lg:text-2xl text-white"
          />
        </button>
      </div>
      <section className=" bg-yellow-700- text-center bg-pink-400- md:mt-[100px] xs:mt-[50px] relative">
        <div className="font-semibold font-outfit md:text-[60px] xs:text-[35px] text-white ">
          How We
          <p className="text-transparent bg-gradient-to-tl from-tosca to-contessa font-outfit font-semibold md:text-[70px] xs:text-[35px] justify-center flex  bg-clip-text">
            Hire?
          </p>
        </div>
        <div className="absolute top-[-90px] left-[460px] xs:hidden xl:block">
          <Image
            src={Images.ShadowTwo}
            alt="/"
            height={350}
            width={500}
            className="opacity-[50%]"
          />
        </div>
      </section>
      <section className="relative">
        <div className=" xl:block xs:hidden mt-[50px] text-white">
          <Image
            src={Images.ARROW_GROUP}
            width={80}
            height={80}
            alt="arrowimg"
            className="w-[600px] h-[1600px] ml-[135px] text-white "
          />
        </div>
        <div className="xl:hidden    mt-[50px] text-white">
          <Image
            src={Images.ARROW_GROUP_Two}
            width={80}
            height={80}
            alt="arrowimg"
            className="lg:w-[280px] lg:h-[1400px] md:h-[1850px]  xs:h-[2200px] xs:w-[100px] ml-[0px] text-white "
          />
        </div>
        <div className="absolute xl:top-[-50px] xl:left-[684px] md:top-[-30px] lg:left-[109px] md:left-[20px] xs:left-[20px]  xs:top-[-30px]  ">
          <Image src={Images.START_BTN} height={60} width={60} alt="btnImage" />
        </div>
        <div className="absolute xl:top-[322px] xl:left-[127px] lg:top-[135px] lg:left-[109px] md:left-[20px] md:top-[154px]  xs:left-[20px]  xs:top-[140px]">
          <Image
            src={Images.START_BTN}
            height={60}
            width={60}
            alt="btnImage ]"
          />
        </div>
        <div className="absolute xl:top-[570px] xl:left-[127px] lg:top-[382px] lg:left-[109px] md:left-[20px] md:top-[515px] xs:left-[20px]  xs:top-[635px]">
          <Image
            src={Images.START_BTN}
            height={60}
            width={60}
            alt="btnImage ]"
          />
        </div>
        <div className="absolute xl:top-[788px] xl:left-[127px] lg:top-[628px] lg:left-[109px] md:left-[20px] md:top-[832px] xs:left-[20px]  xs:top-[992px]">
          <Image
            src={Images.START_BTN}
            height={60}
            width={60}
            alt="btnImage ]"
          />
        </div>
        <div className="absolute xl:bottom-[515px] xl:left-[127px] lg:bottom-[453px] lg:left-[109px] md:left-[20px] md:bottom-[628px] xs:left-[20px]  xs:bottom-[810px]">
          <Image
            src={Images.START_BTN}
            height={60}
            width={60}
            alt="btnImage "
          />
        </div>
        <div className="absolute xl:bottom-[265px] xl:left-[127px] lg:bottom-[218px] lg:left-[109px] md:left-[20px] md:bottom-[287px] xs:left-[20px]  xs:bottom-[422px]">
          <Image
            src={Images.START_BTN}
            height={60}
            width={60}
            alt="btnImage "
          />
        </div>
        <div className=" xl:hidden absolute xl:bottom-[285px] xl:left-[127px] md:bottom-[-15px] lg:left-[109px] md:left-[20px] xs:left-[20px] xs:bottom-[-10px]  ">
          <Image
            src={Images.START_BTN}
            height={60}
            width={60}
            alt="btnImage "
          />
        </div>
        <div>
          <div className=" absolute  xl:top-[190px]  xl:left-[300px] xl:w-[880px]  lg:top-[16px]  lg:left-[250px] lg:w-[680px]  md:top-[0px]  md:left-[130px] md:w-[610px] xs:left-[80px] xs:w-[280px] xs:top-[0px] heigh ">
            {imagesAndLabels.map((item, index) => (
              <div
                key={index}
                className=" bg-gradient-to-b from-Contessa  to-Tosca p-[1px] mt-[50px] rounded-2xl"
              >
                <div
                  className={`lg:flex xs:block  bg-gradient-to-br from-[#101010] via-[#101010] to-[#481e1f] rounded-2xl   ${
                    item.id == 0
                      ? "  flex  items-center p-[30px] "
                      : item.id == 1
                      ? "  flex  items-center p-[30px]    "
                      : item.id == 2
                      ? " flex  items-center p-[30px]  "
                      : "  flex  items-center p-[30px]"
                  }`}
                >
                  <div>
                    <div className="border-2 border-Contessa rounded-full overflow-hidden py-3 px-3 xs:w-[70px] lg:w-full md:mb-[20px] lg:mb-0">
                      <div className="relative rounded-full w-[40px] h-[40px]">
                        <div className={`rounded-full overflow-hidden }`}>
                          <Image
                            src={item.image}
                            alt=" logo"
                            width={40}
                            height={40}
                            className={`absolute transition-opacity duration-1000 ${
                              item.id == 0
                                ? "w-[40px] h-[40px]"
                                : item.id == 1
                                ? "w-[40px] h-[40px]"
                                : item.id == 2
                                ? "w-[40px] h-[40px]"
                                : "w-[40px] h-[40px]"
                            } ${
                              currentImageIndex === index
                                ? "opacity-0"
                                : "opacity-100"
                            }
                            `}
                          />
                          <Image
                            src={item.filledIcon}
                            alt=" logo"
                            width={40}
                            height={40}
                            className={`absolute transition-opacity duration-1000  ${
                              item.id == 0
                                ? "w-[40px] h-[40px]"
                                : item.id == 1
                                ? "w-[40px] h-[40px]"
                                : item.id == 2
                                ? "w-[40px] h-[40px]"
                                : "w-[40px] h-[40px]"
                            }
                            ${
                              currentImageIndex === index
                                ? "opacity-100"
                                : "opacity-0"
                            }`}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div>
                    <div className="xl:ml-[3rem] lg:ml-[2rem] md:ml-[12px] xs:ml-0 xs:mt-[10px]  sm:mt-0  xl:text-[30px] md:text-[30px] xs:text-[18px]  sm:pb-[10px]  text-white font-outfit font-normal not-italic">
                      <LabelComponent label={item.label} />
                    </div>
                    <div className="xl:ml-[3rem] lg:ml-[2rem] md:ml-[12px] xs:ml-0 xs:mt-[10px]  sm:mt-0 md:text-[16px]  xl:text-[18px] xs:text-[18px] xs:pb-6   text-white font-outfit font-light not-italic">
                      <LabelComponent label={item.label_second} />
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
      <section className="flex justify-center  items-center md:mt-[-47px] xs:mt-[20px]">
        {imagesAndLabelsTwo.map((item, index) => (
          <div
            key={index}
            className=" bg-gradient-to-b from-Contessa  to-Tosca p-[1px] mt-[50px] rounded-2xl w-[300px] h-[280px]"
          >
            <div
              className={`relative flex bg-gradient-to-br from-[#101010] via-[#101010] to-[#481e1f] rounded-2xl w-[298px] h-[278px]   ${
                item.id == 0
                  ? "  flex justify-center items-center flex-col p-[30px] "
                  : item.id == 1
              }`}
            >
              <div>
                <div className=" border-2 border-Contessa rounded-full overflow-hidden py-6 px-6">
                  <div className=" rounded-full w-[40px] h-[40px]">
                    <div className={`rounded-full overflow-hidden }`}>
                      <Image
                        src={item.image}
                        alt=" logo"
                        width={40}
                        height={40}
                        className={`absolute transition-opacity duration-1000 
                         
                         ${
                           currentImageIndex === index
                             ? "opacity-0"
                             : "opacity-100"
                         }
                            `}
                      />
                      <Image
                        src={item.filledIcon}
                        alt=" logo"
                        width={40}
                        height={40}
                        className={`absolute transition-opacity duration-1000 
                            ${
                              currentImageIndex === index
                                ? "opacity-100"
                                : "opacity-0"
                            }`}
                      />
                    </div>
                  </div>
                  <div className="absolute top-[24px]  left-[154px] z-10">
                    <Image
                      src={Images.blue_tick}
                      height={80}
                      width={80}
                      alt="btnImage "
                      className={`w-[50px] h-[50px] transition-opacity duration-1000 ${
                        currentImageIndex === index
                          ? "opacity-100"
                          : "opacity-0"
                      }`}
                    />
                  </div>
                </div>
              </div>
              <div>
                <div className="  xs:ml-0 xs:mt-[10px] text-center   sm:mt-[30px] lg:text-[28px] xl:text-[30px] xs:text-[22px] xs:pb-6 sm:pb-0  text-white font-outfit font-normal not-italic">
                  <LabelComponent label={item.label} />
                </div>
              </div>
            </div>
          </div>
        ))}
      </section>

      <div
        id="Hiredeveloper"
        className="flex justify-center md:my-[100px] xs:my-[50px] bg-red-700-"
      >
        <div className="rounded-3xl md:rounded-[30px] flex justify-center mx-5 lg:mx-20 w-full max-w-screen-2xl m-auto bg-gradient-to-r from-tundora to-CodGray py-7 px-2 md:p-10">
          <div className="text-center">
            <h1 className="text-white font-bold text-xl md:text-4xl ">
              {Strings.HIRE_MANAGE}
            </h1>
            <h1 className="text-white font-extralight text-base md:text-2xl flex justify-center my-5 lg:my-10">
              {Strings.GET_A_FULLY_REMOTE}
            </h1>
            <button
              onClick={handleButtonClick}
              className="rounded-full bg-gradient-to-t from-contessa to-tosca  hover:bg-gradient-to-b hover:from-Contessa hover:to-Tosca py-3 md:py-5 px-8 md:px-10 transition-transform hover:scale-110 transform duration-700"
            >
              <h1 className="font-light text-base md:text-2xl text-white">
                {Strings.HIRE_A_TOP_ENGINEER}
              </h1>
            </button>
          </div>
          <Hiretopengineer isOpen={isOpen} setIsOpen={setIsOpen} />
        </div>
      </div>
      <Hiretopengineer isOpen={isOpen} setIsOpen={setIsOpen} />
      <footer>
        <Footer />
      </footer>
    </div>
  );
};

export default HowWeHire;
