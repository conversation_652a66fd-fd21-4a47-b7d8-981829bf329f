import "../app/globals.css";

import { DeleteOutlined, DownOutlined } from "@ant-design/icons";
import { Images, Strings } from "@/constant";
import React, { useEffect, useRef, useState } from "react";

import Image from "next/image";
import Link from "next/link";
import Swal from "sweetalert2";
import axios from "axios";
import { useRouter } from "next/router";
import { useScreenShare } from "@/context/ScreenShareContext";

// import * as blazeface from '@tensorflow-models/blazeface';
// import * as tf from '@tensorflow/tfjs';

const StartVetting = () => {
  const { setStream } = useScreenShare();
  const router = useRouter();
  const [step, setStep] = useState(1);
  const [skills, setSkills] = useState<string[]>([""]);
  const [error, setError] = useState<string | null>(null);
  const [rememberMe, setRememberMe] = useState(false);
  const [isDesktop, setIsDesktop] = useState(false);
  const videoRef = useRef<HTMLVideoElement | null>(null);
  const [audioDevices, setAudioDevices] = useState<MediaDeviceInfo[]>([]);
  const [videoDevices, setVideoDevices] = useState<MediaDeviceInfo[]>([]);
  const [audioLevel, setAudioLevel] = useState(0);
  const [isRecording, setIsRecording] = useState(false);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const microphoneRef = useRef<MediaStreamAudioSourceNode | null>(null);
  const mediaStreamRef = useRef<MediaStream | null>(null);
  const [selectedDevice, setSelectedDevice] = useState<string>("");
  const [selectedAudio, setSelectedAudio] = useState<string>("");
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const [recordingSuccess, setRecordingSuccess] = useState(false);
  const { email, name } = router.query;
  const [isCameraAccessible, setIsCameraAccessible] = useState(false);

  const [userInfo, setUserInfo] = useState({ email: "", name: "" });
  const [isLoadingDevices, setIsLoadingDevices] = useState(true);
  const [isChecking, setIsChecking] = useState(true);
  const [canProceed, setCanProceed] = useState(true);
  const [predefinedSkills, setPredefinedSkills] = useState<string[]>([]);
  
  // useEffect(() => {
  //   if (typeof window !== "undefined") {
  //     const storedEmail = localStorage.getItem("candidate_email");
  //     const storedName = localStorage.getItem("candidate_name");
      

  //     if (email && name) {
  //       localStorage.setItem("candidate_email", email as string);
  //       localStorage.setItem("candidate_name", name as string);
  //       setUserInfo({ email: email as string, name: name as string });
  //     } else if (!email && !name && storedEmail && storedName) {
  //       setUserInfo({ email: storedEmail, name: storedName });
  //     }
  //   }
  // }, [router.query]);

  // useEffect(() => {
  //   if (!router.isReady) return; 

  //   const id = Array.isArray(router.query.id) ? router.query.id[0] : router.query.id; 
  //   const gptVettingUserData = axios.get(`${process.env.NEXT_PUBLIC_API_BASE_URL}gptVetting/get?id=${id}`);
  //   console.log("rrrrrr", gptVettingUserData);

  //   if (id) {
  //     console.log("Extracted ID:", id);
  //     localStorage.setItem("userId", id); 
  //   } else {
  //     console.log("No ID found in query.");
  //   }
  // }, [router.isReady, router.query]);

  useEffect(() => {
    const fetchData = async () => {
      if (!router.isReady) return;

      const id = Array.isArray(router.query.id)
        ? router.query.id[0]
        : router.query.id;

      if (!id) {
        console.log("No ID found in query.");
        return;
      }

      try {
        const response = await axios.get(
          `${process.env.NEXT_PUBLIC_API_BASE_URL}gptVetting/get?id=${id}`
        );

        const isSuccess = response.data?.gptVettingData?.success;

        if (isSuccess === true) {
          setCanProceed(false); // ✅ block user from continuing
        }
        
        const techStack = response.data?.gptVettingData?.techStack;

        if (Array.isArray(techStack)) {
          setPredefinedSkills(techStack);
        } else {
          console.warn("techStack is not an array:", techStack);
        }

        localStorage.setItem("userId", id); // ✅ only store if valid
      } catch (error) {
        console.error("Error fetching vetting user data:", error);
      } finally {
        setIsChecking(false); // ✅ end loading state
      }
    };

    fetchData();
  }, [router.isReady, router.query.id, predefinedSkills]);

  useEffect(() => {
    const isManuallyEmpty = skills.length === 1 && skills[0] === "";
    if (isManuallyEmpty && predefinedSkills.length > 0) {
      setSkills(predefinedSkills);
      localStorage.setItem("skills", JSON.stringify(predefinedSkills));
    }
  }, [predefinedSkills]);
  
  const addSkill = () => {
    if (skills.length < 5) {
      localStorage.setItem("skills", JSON.stringify(skills));
      setSkills([...skills, ""]);
    }
  };

  const deleteSkill = (index: number) => {
    const updatedSkills = [...skills];
    updatedSkills.splice(index, 1);
    setSkills(updatedSkills);
  };

  const handleChange = (index: number, value: string) => {
    const newSkills = [...skills];
    newSkills[index] = value;
    setSkills(newSkills);
    validateSkills(newSkills);
  };

  const validateSkills = (skillsArray: string[]) => {
    const nonEmptySkills = skillsArray.filter((skill) => skill.trim() !== "");
    const uniqueSkills = new Set(nonEmptySkills);

    if (uniqueSkills.size < nonEmptySkills.length) {
      setError("Same skills are not allowed.");
      Swal.fire({
        toast: true,
        position: "top",
        showConfirmButton: false,
        timer: 3000,
        icon: "error",
        title: "Same skills are not allowed.",
        padding: "10px 20px",
      });
    } else {
      setError("");
    }
  };

  const handleContinueClick = async () => {
    localStorage.removeItem("skills");
  
    if (error) {
      Swal.fire({
        toast: true,
        position: "top",
        timer: 3000,
        icon: "error",
        title: "Cannot Proceed. Fix the errors before continuing.",
        showConfirmButton: false,
      });
      return;
    }
  
    if (skills.length < 0 || skills.some((skill) => skill.trim() === "")) {
      Swal.fire({
        toast: true,
        position: "top",
        timer: 3000,
        icon: "error",
        title: "Please define at least 1 skill.",
        showConfirmButton: false,
      });
      return;
    }
  
    localStorage.setItem("skills", JSON.stringify(skills));
    setStep(step + 1);
  };
  

  useEffect(() => {
    const rememberMeValue = localStorage?.getItem("rememberMe");
    if (rememberMeValue === "true") {
      setRememberMe(true);
    }
  }, []);

  useEffect(() => {
    const initializeAudio = async () => {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({
          audio: true,
        });
        mediaStreamRef.current = stream;
        audioContextRef.current = new (window.AudioContext ||
          window.AudioContext)();
        analyserRef.current = audioContextRef.current.createAnalyser();
        analyserRef.current.fftSize = 256;

        const bufferLength = analyserRef.current.frequencyBinCount;
        const dataArray = new Uint8Array(bufferLength);

        microphoneRef.current =
          audioContextRef.current.createMediaStreamSource(stream);
        microphoneRef.current.connect(analyserRef.current);

        const updateAudioLevel = () => {
          if (isRecording) {
            analyserRef.current?.getByteFrequencyData(dataArray);
            const sum = dataArray.reduce((acc, val) => acc + val, 0);
            const average = sum / dataArray.length;
            setAudioLevel(average);
          }
          requestAnimationFrame(updateAudioLevel);
        };

        updateAudioLevel();
      } catch (err) {
        console.error("Error accessing microphone:", err);
        Swal.fire({
          icon: "error",
          title: "Microphone Not Found",
          toast: true,
          position: "top",
          timer: 3000,
          showConfirmButton: false,
        });
      }
    };

    if (isRecording) {
      initializeAudio();
    } else {
      microphoneRef.current?.disconnect();
      analyserRef.current?.disconnect();
      audioContextRef.current?.close();
      mediaStreamRef.current?.getTracks().forEach((track) => track.stop());
    }

    return () => {
      microphoneRef.current?.disconnect();
      analyserRef.current?.disconnect();
      audioContextRef.current?.close();
      mediaStreamRef.current?.getTracks().forEach((track) => track.stop());
    };
  }, [isRecording]);

  const handleSpeakClick = async () => {
    if (!isRecording) {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({
          audio: true,
        });
        const mediaRecorder = new MediaRecorder(stream);
        mediaRecorderRef.current = mediaRecorder;

        mediaRecorder.ondataavailable = (event) => {
          if (event.data.size > 0) {
            audioChunksRef.current.push(event.data);
          }
        };

        mediaRecorder.onstop = () => {
          const audioBlob = new Blob(audioChunksRef.current, {
            type: "audio/wav",
          });
          setAudioBlob(audioBlob);
          audioChunksRef.current = [];
          setRecordingSuccess(true);
        };

        mediaRecorder.start();
        setIsRecording(true);
      } catch (err) {
        console.error("Error accessing microphone:", err);
        Swal.fire({
          icon: "error",
          title: "Microphone Error Could not access microphone.",
          toast: true,
          position: "top",
          timer: 3000,

          showConfirmButton: false,
        });
      }
    }
  };

  const handleStopClick = () => {
    if (isRecording && mediaRecorderRef.current) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }
  };

  const playRecordedAudio = () => {
    if (audioBlob) {
      const audioURL = URL.createObjectURL(audioBlob);
      const audio = new Audio(audioURL);
      audio.play();
    }
  };

  useEffect(() => {
    if (!isRecording && audioBlob) {
      playRecordedAudio();
    }
  }, [isRecording, audioBlob]);

  useEffect(() => {
    const checkIsDesktop = () => {
      setIsDesktop(window.innerWidth >= 1024);
    };
    checkIsDesktop();
    window.addEventListener("resize", checkIsDesktop);
    return () => {
      window.removeEventListener("resize", checkIsDesktop);
    };
  }, []);

  const handleStartInterview = async () => {
    try {
      // First check if screen capture is supported
      if (!navigator.mediaDevices || !navigator.mediaDevices.getDisplayMedia) {
        throw new Error("Screen sharing is not supported in your browser");
      }

      // Request screen sharing with basic options
      const displayMediaOptions = {
        video: true,
        audio: false
      };

      let screenStream: MediaStream;
      try {
        screenStream = await navigator.mediaDevices.getDisplayMedia(displayMediaOptions);
      } catch (err: unknown) {
        // Handle browser-specific errors
        if (err instanceof Error && (err.name === "NotFoundError" || err.name === "TypeError")) {
          Swal.fire({
            icon: "error",
            title: "Screen sharing not available",
            text: "Please make sure you're using a supported browser and have granted screen sharing permissions.",
            toast: true,
            position: "top",
            timer: 5000,
            showConfirmButton: false,
          });
          return;
        }
        throw err;
      }

      if (!screenStream) {
        throw new Error("Failed to initialize screen sharing stream");
      }

      // Get screen sharing settings
      const track = screenStream.getVideoTracks()[0];
      const settings = track.getSettings();

      // More permissive display surface check
      if (settings.displaySurface && 
          settings.displaySurface !== "monitor" && 
          settings.displaySurface !== "window" && 
          settings.displaySurface !== "browser") {
        Swal.fire({
          icon: "warning",
          title: "Screen sharing recommendation",
          text: "For best results, please share your entire screen rather than a single window.",
          toast: true,
          position: "top",
          timer: 5000,
          showConfirmButton: true,
        });
      }

      // Handle screen share stop
      track.addEventListener('ended', () => {
        screenStream.getTracks().forEach(track => track.stop());
        Swal.fire({
          icon: "info",
          title: "Screen sharing stopped",
          text: "You have stopped sharing your screen.",
          toast: true,
          position: "top",
          timer: 3000,
          showConfirmButton: false,
        });
      });

      // Get microphone access
      let micStream;
      try {
        micStream = await navigator.mediaDevices.getUserMedia({
          audio: true,
        });
      } catch (micError) {
        screenStream.getTracks().forEach(track => track.stop());
        Swal.fire({
          icon: "error",
          title: "Microphone access failed",
          text: "Please grant microphone permissions to continue.",
          toast: true,
          position: "top",
          timer: 3000,
          showConfirmButton: false,
        });
        return;
      }

      // Rest of your existing code for handling the streams
      setStream(screenStream);
      router.push({
        pathname: "/ai-interviewer",
        query: { skills: skills.join(",") },
      });

    } catch (error) {
      console.error("Screen sharing error:", error);
      Swal.fire({
        icon: "error",
        title: "Screen sharing failed",
        text: "Please try again or use a different browser (Chrome recommended).",
        toast: true,
        position: "top",
        timer: 5000,
        showConfirmButton: false,
      });
    }
  };


  useEffect(() => {
    let mediaStream: MediaStream | null = null;

    if (isRecording) {
      navigator.mediaDevices
        .getUserMedia({ video: true, audio: true })
        .then((stream) => {
          if (videoRef.current) {
            videoRef.current.srcObject = stream;
            mediaStream = stream;
          }
        })
        .catch((err) => {
          console.error("Error accessing media devices:", err);
        });
    } else {
      if (mediaStream) {
        (mediaStream as MediaStream)
          .getTracks()
          .forEach((track) => track.stop());
      }
      if (videoRef.current) {
        videoRef.current.srcObject = null;
      }
    }
    return () => {
      if (mediaStream) {
        mediaStream.getTracks().forEach((track) => track.stop());
      }
    };
  }, [isRecording]);

  useEffect(() => {
    if (step === 3) {
      // Reset device states when entering step 3
      setSelectedDevice("");
      setSelectedAudio("");
      setVideoDevices([]);
      setAudioDevices([]);
      setIsLoadingDevices(true);
      
      const initializeDevices = async () => {
        try {
          // Request permissions and enumerate devices
          const stream = await navigator.mediaDevices.getUserMedia({ 
            video: true, 
            audio: true 
          });
          
          // Stop the initial stream since we'll create new ones for preview
          stream.getTracks().forEach(track => track.stop());
          
          // Now enumerate devices after permissions are granted
          const devices = await navigator.mediaDevices.enumerateDevices();
          const videoDevs = devices.filter(device => device.kind === 'videoinput');
          const audioDevs = devices.filter(device => device.kind === 'audioinput');
          
          setVideoDevices(videoDevs);
          setAudioDevices(audioDevs);
        } catch (error) {
          console.error('Error initializing devices:', error);
          Swal.fire({
            icon: "error",
            title: "Permission Required",
            text: "Please allow access to your camera and microphone to continue with the interview.",
            showConfirmButton: true,
          }).then((result) => {
            if (result.isConfirmed) {
              // Try again if user clicks OK
              initializeDevices();
            } else {
              // Go back to previous step if user cancels
              setStep(step - 1);
            }
          });
        } finally {
          setIsLoadingDevices(false);
        }
      };

      initializeDevices();

      // Add device change listener
      navigator.mediaDevices.addEventListener('devicechange', initializeDevices);
      
      return () => {
        navigator.mediaDevices.removeEventListener('devicechange', initializeDevices);
        // Clean up any active streams
        if (videoRef.current?.srcObject) {
          const stream = videoRef.current.srcObject as MediaStream;
          stream.getTracks().forEach(track => track.stop());
        }
      };
    }
  }, [step]);

  useEffect(() => {
    const startVideoStream = async () => {
      if (selectedDevice && videoRef.current) {
        try {
          const stream = await navigator.mediaDevices.getUserMedia({
            video: { deviceId: selectedDevice },
          });
          setIsCameraAccessible(true);
          videoRef.current.srcObject = stream;
        } catch (error) {
          console.error("Error accessing video stream:", error);
        }
      }
    };

    startVideoStream();

    return () => {
      if (videoRef.current?.srcObject) {
        const stream = videoRef.current.srcObject as MediaStream;
        stream.getTracks().forEach((track) => track.stop());
      }
    };
  }, [selectedDevice]);

  return (
    <>
      {!isDesktop && (
        <div className="z-10 fixed bg-white flex justify-center items-center h-full w-full">
          <h3 className="font-normal text-black text-sm mx-5 text-center">
            Please open AI interview on desktop only
          </h3>
        </div>
      )}
      {isChecking ? null : !canProceed ? (
        <div className="z-10 fixed bg-white- flex justify-center items-center h-full w-full">
          <h3 className="font-normal text-black- text-sm mx-5 text-center">
            You have already completed the interview. You cannot proceed again.
          </h3>
          <Link href="/gpt-vetting" className="text-blue-500 underline">
              Go back to Home
          </Link>
        </div>
      ) : (
      <div className="flex items-center justify-center flex-col h-screen w-full">
        {step === 1 && (
          <div className="flex justify-center items-center px-auto">
            <div className="text-center space-y-4">
              <h3 className="font-normal text-xl text-white w-[45%] text-center mx-auto">
                Please note that this{" "}
                <strong className="bg-gradient-to-l from-contessa to-tosca text-transparent bg-clip-text font-semibold">
                  ~7 minute
                </strong>{" "}
                interview will be with an{" "}
                <strong className="bg-gradient-to-l from-contessa to-tosca text-transparent bg-clip-text font-semibold">
                  eRemoteHire
                </strong>
                . You will answer each question by speaking out loud, so find a
                quiet spot and make sure your internet connection is stable.
                Once this portion is complete there will be a{" "}
                <strong className="bg-gradient-to-l from-contessa to-tosca text-transparent bg-clip-text font-semibold">
                  25-minute
                </strong>{" "}
                coding exercise right after.
              </h3>
              <div className="flex justify-center items-center">
              <button
                className="flex text-white jusity-content items-center bg-gradient-to-r from-[#481e1f] to-[#6b443a] transition-transform hover:scale-105 transform duration-700 px-6 py-3 rounded-full"
                onClick={() => setStep(step + 1)}
              >
                Continue
              </button>
              </div>
            </div>
          </div>
        )}
        {step === 2 && (
          <div className="text-center">
            <h2 className="mt-4 font-semibold text-4xl text-white">
              Please define your skills
            </h2>
            <div className="space-y-4 my-6">
              {skills.map((skill, index) => (
                <div
                  key={index}
                  className="flex justify-center items-center relative"
                >
                  <div className="bg-gradient-to-r from-[#481e1f] to-[#6b443a] p-[1px] flex justify-self-center rounded-lg w-[320px]">
                    <input
                      type="text"
                      placeholder="Define skill"
                      value={skill}
                      onChange={(e) => handleChange(index, e.target.value)}
                      className="inset-0 text-white bg-[linear-gradient(to_top,_#241415,_black)] w-full h-12 rounded-lg px-3 outline-none bg-black xs:text-sm lg:text-lg"
                    />
                  </div>
                  {index > 0 && (
                    <button
                      onClick={() => deleteSkill(index)}
                      className="text-red-500 absolute right-0"
                      title="Delete Skill"
                    >
                      <DeleteOutlined />
                    </button>
                  )}
                </div>
              ))}
              {skills.length < 5 && (
                <div className="flex justify-center w-full">
                  <button
                    onClick={addSkill}
                    className="flex text-white items-center justify-center mx-auto bg-[linear-gradient(to_top,_#241415,_black)] border border-opacity-50 border-tosca hover:border-opacity-100 transition-transform hover:scale-105 transform duration-700 px-6 py-3 rounded-full"
                  >
                    Add up to {5 - skills.length} more skills (optional)
                  </button>
                </div>
              )}
            </div>
              <div className="flex justify-center w-full">
            <button
              className="flex text-white justify-center bg-gradient-to-r from-[#481e1f] to-[#6b443a] transition-transform hover:scale-105 transform duration-700 px-6 py-3 rounded-full"
              onClick={handleContinueClick}
            >
              Continue
            </button>
            </div>
            <p className="font-normal text-sm text-white mt-2">
              {Strings.Note}
            </p>
          </div>
        )}
        {step === 3 && (
          <div className="flex justify-center  items-center w-full">
            <div className="text-center">
              <h3 className="font-semibold text-4xl text-white">
                Check camera, mic, and share screen
              </h3>
              <h3 className="my-6 font-normal text-xl text-white w-[60%] mx-auto">
                We use audio, video, and screen sharing to generate an accurate
                assessment & proctoring score. Please note that the recording of
                your screen will be included in the AI interview report.
              </h3>
              <div className="flex items-center justify-center gap-x-4 mb-2">
                <div className="relative w-[320px]">
                  <select
                    title="Select video source"
                    className="text-white text-sm pr-7 p-2 bg-[linear-gradient(to_top,_#241415,_black)] outline-none border border-tosca border-opacity-50 rounded-md h-12 w-full appearance-none"
                    value={selectedDevice}
                    onChange={(e) => setSelectedDevice(e.target.value)}
                    disabled={isLoadingDevices}
                  >
                    <option value="" disabled>Select video source</option>
                    {videoDevices.map((device) => (
                      <option
                        className="text-black hover:bg-tosca"
                        key={device.deviceId}
                        value={device.deviceId}
                      >
                        {device.label || `Camera ${videoDevices.indexOf(device) + 1}`}
                      </option>
                    ))}
                  </select>
                  <div className="absolute inset-y-0 right-0 flex items-center px-4 pointer-events-none">
                    <DownOutlined className="h-3 w-3 text-white" />
                  </div>
                </div>
                <div className="relative w-[320px]">
                  <select
                    title="Select microphone"
                    className="text-white text-sm pr-7 p-2 bg-[linear-gradient(to_top,_#241415,_black)] outline-none border border-tosca border-opacity-50 rounded-md h-12 w-full appearance-none"
                    value={selectedAudio}
                    onChange={(e) => setSelectedAudio(e.target.value)}
                    disabled={isLoadingDevices}
                  >
                    <option value="" disabled>Select microphone</option>
                    {audioDevices.map((device) => (
                      <option
                        className="text-black hover:bg-tosca"
                        key={device.deviceId}
                        value={device.deviceId}
                      >
                        {device.label || `Microphone ${audioDevices.indexOf(device) + 1}`}
                      </option>
                    ))}
                  </select>
                  <div className="absolute inset-y-0 right-0 flex items-center px-4 pointer-events-none">
                    <DownOutlined className="h-3 w-3 text-white" />
                  </div>
                </div>
              </div>
              {isLoadingDevices && (
                <div className="text-white text-center mt-2">
                  Loading available devices...
                </div>
              )}
              {!isLoadingDevices && videoDevices.length === 0 && audioDevices.length === 0 && (
                <div className="text-red-500 text-center mt-2">
                  No media devices found. Please ensure your camera and microphone are connected and permissions are granted.
                </div>
              )}
              <div className="flex items-center justify-center gap-x-4">
                <div className="h-48 w-[320px] rounded-md bg-gradient-to-r from-[#481e1f] to-[#6b443a] p-[2px]">
                  <div className="h-full w-full rounded-md bg-black">
                    <video
                      ref={videoRef}
                      autoPlay
                      playsInline
                      muted
                      className="h-full w-full rounded-md object-cover"
                      style={{ transform: 'scaleX(-1)'}}
                    />
                  </div>
                </div>
                <div className="h-48 w-[320px] rounded-md bg-gradient-to-r from-[#481e1f] to-[#6b443a] p-[2px]">
                  <div className="h-full w-full rounded-md bg-black p-4">
                    <h3 className="font-normal text-sm text-white">
                      Speak and pause to check your microphone, you will hear
                      your voice{" "}
                      <strong className="text-white font-semibold">
                        (mandatory)
                      </strong>
                      .
                    </h3>
                    <div className="my-2 bg-white rounded-lg p-2 relative">
                      <h3 className="text-[#2A2B2F] font-normal text-sm">
                        Level input
                      </h3>
                      {recordingSuccess && (
                        <Image
                          src={Images.Checkicon}
                          alt="/"
                          height={500}
                          width={500}
                          className="h-5 w-5 absolute top-2 right-2"
                        />
                      )}
                      <div className="mt-2">
                        <div className="w-full bg-white flex justify-between">
                          {Array.from({ length: 10 }, (_, index) => (
                            <div
                              key={index}
                              className="flex justify-center mx-1"
                            >
                              <div
                                className={`h-7 w-[14px] rounded-sm ${audioLevel > index * 10
                                  ? "bg-tosca"
                                  : "bg-tosca bg-opacity-50"
                                  }`}
                              />
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                    <button
                      onClick={isRecording ? handleStopClick : handleSpeakClick}
                      className={`font-normal text-sm  px-4 rounded-full transition-transform duration-300 transform hover:scale-[105%] ${isRecording
                        ? "bg-[linear-gradient(to_top,_#241415,_black)] outline outline-[2px] outline-[#8d3f42]/50 py-[7px]"
                        : "bg-gradient-to-r from-[#481e1f] to-[#6b443a] py-2"
                        } text-white`}
                    >
                      {isRecording ? "Pause" : "Speak"}
                    </button>
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-1 text-white justify-center mt-4">
                <input
                  type="checkbox"
                  className="form-checkbox h-3 w-3 bg-black cursor-pointer"
                  checked={rememberMe}
                  onChange={() => {
                    setRememberMe(!rememberMe);
                  }}
                  title="Remember me"
                />
                <p>I agree to the</p>
                <Link
                  href="/interview-terms-condition"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="underline text-tosca"
                >
                  <span className="bg-gradient-to-r from-tosca to-contessa text-transparent bg-clip-text">
                    terms & conditions
                  </span>
                </Link>

                <p>of this AI interview process.</p>
              </div>
              <button
                disabled={!rememberMe || !isCameraAccessible || !selectedAudio}
                onClick={handleStartInterview}
                className="bg-gradient-to-r disabled:opacity-50 from-[#481e1f] to-[#6b443a] transition-transform hover:scale-105 transform duration-700 px-6 py-3 rounded-full mt-6"
              >
                Share screen and start the interview
              </button>
            </div>
          </div>
        )}
        {step > 1 ? (
          <div className="absolute top-5 left-5">
            <button
              onClick={() => setStep(step - 1)}
              className="bg-gradient-to-r from-[#481e1f] to-[#6b443a] transition-transform hover:scale-105 p-[1px] transform duration-700 rounded-lg"
            >
              <div className="bg-[linear-gradient(to_top,_#241415,_black)] px-4 py-2 h-full w-full rounded-lg">
                Back
              </div>
            </button>
          </div>
        ) : (
          <div className="absolute top-5 left-5">
            <Link href={"/gpt-vetting"}>
              <button className="bg-gradient-to-r text-white from-[#481e1f] to-[#6b443a] transition-transform hover:scale-105 p-[1px] transform duration-700 rounded-lg">
                <div className="bg-[linear-gradient(to_top,_#241415,_black)] px-4 py-2 h-full w-full rounded-lg">
                  Exit
                </div>
              </button>
            </Link>
          </div>
        )}
        <div className="absolute bottom-5">
          <h3 className="font-normal text-sm text-white">
            Powered by{" "}
            <span className="bg-gradient-to-l font-medium from-contessa to-tosca text-transparent bg-clip-text">
              eRemoteHire
            </span>
          </h3>
        </div>
      </div>
      )}
    </>
  );
};

export default StartVetting;
