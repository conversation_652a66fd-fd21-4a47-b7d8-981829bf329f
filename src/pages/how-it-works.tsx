"use-client";

import "../app/globals.css";

import Hiretopengineer, { toggleModal } from "@/Component/Hiretopengineer";
import { Images, Strings } from "@/constant";
import { motion, useTransform, useViewportScroll } from "framer-motion";
import { useEffect, useState } from "react";

import AccordionWrapper from "@/Component/AccordionWrapper";
import Cookie from "@/Component/cookies";
import CountComponent from "@/Component/CountCompent";
import { Footer } from "@/Component/footer";
import ImageSliderComponent from "@/Component/HomePage/ImageSlider";
import { LabelComponent } from "@/Component/label";
import SimplePricing from "@/Component/SimplePricing";
import { trackPageView } from "@/utils/helper";
import axios from "axios";
import { GetServerSideProps } from "next";
import Image from "next/image";
import React from "react";
import NavbarComponent from "../Component/Navbar";

export const getServerSideProps: GetServerSideProps = async () => {
  try {
    const [faqResponse] = await Promise.all([
      axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}devdashboard/getFAQData?pageName=how-it-works`
      ),
    ]);

    const faqData = faqResponse?.data?.faqData || [];

    return {
      props: {
        faqData,
      },
    };
  } catch (error) {
    console.error("Failed to fetch data:", error);
    return {
      props: {
        faqData: [],
      },
    };
  }
};

const Howitswork = ({ logos, faqData }: any) => {
  const [isTransitioned, setIsTransitioned] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isOpen, setIsOpen] = useState(false);

  const handleButtonClick = () => {
    toggleModal(isOpen, setIsOpen);
  };

  useEffect(() => {
    trackPageView("/how-it-work", "How It Work");
  }, []);

  const { scrollYProgress } = useViewportScroll();
  const scale = useTransform(scrollYProgress, [0, 0.9], [0.5, 2]);
  const imagesAndLabels = [
    {
      id: 0,
      image: Images.ICON,
      label: Strings.PICK,
      filledIcon: Images.ICON_fill,
    },
    {
      id: 1,
      image: Images.ZOOM,
      label: Strings.INTERVIEW,
      filledIcon: Images.ZOOM_fill,
    },
    {
      id: 2,
      image: Images.THREE_DOT,
      label: Strings.GET,
      filledIcon: Images.THREE_DOTT,
    },
    {
      id: 3,
      image: Images.CREDIT,
      label: Strings.TELL,

      filledIcon: Images.FILL_CREDIT,
    },
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      if (imagesAndLabels.length > 0) {
        setCurrentImageIndex(
          (prevIndex) => (prevIndex + 1) % imagesAndLabels.length
        );
      }
    }, 2000);

    return () => {
      clearInterval(timer);
    };
  }, [imagesAndLabels.length]);

  const handleToggleTransition = () => {
    setIsTransitioned((prevState) => !prevState);
  };
  const Icon: React.FC<{ id: number; open: any }> = ({ id, open }) => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth={2}
      stroke="currentColor"
      className={`${
        id === open ? "rotate-180" : ""
      } h-5 w-5 transition-transform`}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M19.5 8.25l-7.5 7.5-7.5-7.5"
      />
    </svg>
  );

  const simplePricingData = [
    {
      title: Strings.TALENT,
      text: Strings.TALENT_TXT,
      btntext: Strings.HIRE_A_TOP_ENGINEER,
      onBtnClick: handleButtonClick,
    },
    {
      title: Strings.COR,
      text: Strings.HOWITWORKP,
      btntext: Strings.BOOK_A_DEMO,
      href: "https://calendly.com/info-remote",
    },
  ];

  return (
    <div>
      <div>
        <Cookie />
      </div>
      <div>
        <NavbarComponent />
      </div>

      <motion.div className="md:my-[100px] xs:my-[50px] xs:mt-[50px]- bg-red-900- ">
        <div className="flex justify-center items-center lg:mt-7- lg:mb-[50px]-">
          <LabelComponent
            label={Strings.HOW_IT_WORK}
            className=" font-semibold xs:text-3xl md:text-[50px] text "
          />
        </div>
        <div className="text-center lg:mt-8 xs:mt-[20px]">
          <p className=" xs:text-[14px] md:text-[17px] font-extralight text-white">
            4 easy steps to have a top 1% engineer onboarded.
          </p>
          <p className=" font-semibold xs:text-[20px] md:text-[28px] text md:mt-[25px] xs:mt-20px]">
            This is done on 4 days in average.
          </p>
        </div>
      </motion.div>
      <div className=" bg-white- md:mt-[150px]- xs:hidden md:hidden lg:block ">
        <div>
          <div className="xs:mt-8- lg:mt-[28px]- md:ml-10- ">
            <ul
              className={`cards-split   ${isTransitioned ? "transition" : ""}`}
              onClick={handleToggleTransition}
            >
              {imagesAndLabels.length > 0 &&
                imagesAndLabels.map((item, index) => (
                  <li
                    key={index}
                    className={`   bg-gradient-to-b from-orange-200  to-Tosca p-[2px] ${
                      item.id == 0
                        ? "card card-1 "
                        : item.id == 1
                        ? "card card-2"
                        : item.id == 2
                        ? "card card-3"
                        : "card card-4"
                    }`}
                  >
                    <div className="content flex justify-center items-center flex-col  w-full h-full  bg-black rounded-[8px]">
                      <div className="border-2 border-Contessa rounded-full overflow-hidden py-[30px] px-[30px] mb-[30px] ">
                        <div className=" rounded-full w-[40px] h-[40px]  ">
                          <div className={`  }`}>
                            <Image
                              src={item.image}
                              alt=" logo"
                              width={40}
                              height={40}
                              className={`absolute  transition-opacity duration-1000 ${
                                item.id == 0
                                  ? "w-[40px] h-[40px] pl-[3px]  "
                                  : item.id == 1
                                  ? "w-[40px] h-[40px] pt-[8px] "
                                  : item.id == 2
                                  ? "w-[40px] h-[40px] pt-[6px] "
                                  : "w-[40px] h-[40px]"
                              } ${
                                currentImageIndex === index
                                  ? "opacity-0"
                                  : "opacity-100"
                              }
                              `}
                            />
                            <Image
                              src={item.filledIcon}
                              alt=" logo"
                              width={40}
                              height={40}
                              className={`absolute transition-opacity duration-1000  ${
                                item.id == 0
                                  ? "w-[40px] h-[40px]  pl-[3px]  "
                                  : item.id == 1
                                  ? "w-[40px] h-[40px] pt-[8px]"
                                  : item.id == 2
                                  ? "w-[40px] h-[40px]  pt-[6px]"
                                  : "w-[40px] h-[40px]"
                              }
                              ${
                                currentImageIndex === index
                                  ? "opacity-100"
                                  : "opacity-0"
                              }`}
                            />
                          </div>
                        </div>
                      </div>
                      <LabelComponent
                        className={`
                         text-[23px] font-bold text-white text-center flex justify-center items-center 
                        ${
                          item.id == 0
                            ? ""
                            : item.id == 1
                            ? ""
                            : item.id == 2
                            ? ""
                            : "mx-[30px]"
                        } `}
                        label={item.label}
                      />
                    </div>
                  </li>
                ))}
            </ul>
          </div>
        </div>
      </div>
      <div className="xs:mt-[40px] xs:block md:block lg:hidden ">
        <ul className="flex justify-center items-center  flex-col md:grid grid-cols-2 md:px-[6rem] lg:px-0">
          {imagesAndLabels.length > 0 &&
            imagesAndLabels.map((item, index) => (
              <li
                key={index}
                className={`h-[300px] w-[250px] flex justify-center items-center mb-[20px]   bg-gradient-to-b from-orange-200  to-Tosca p-[2px] rounded-2xl`}
              >
                <div className="content flex justify-center items-center flex-col  w-full h-full  bg-black rounded-[14px]">
                  <div className="border-2 border-Contessa rounded-full overflow-hidden py-[30px] px-[30px] mb-[30px]">
                    <div className=" rounded-full w-[40px] h-[40px] flex">
                      <div className={`  }`}>
                        <Image
                          src={item.image}
                          alt=" logo"
                          width={40}
                          height={40}
                          className={`absolute transition-opacity duration-1000 ${
                            item.id == 0
                              ? "w-[40px] h-[40px]"
                              : item.id == 1
                              ? "w-[40px] h-[40px]"
                              : item.id == 2
                              ? "w-[40px] h-[40px]"
                              : "w-[40px] h-[40px]"
                          } ${
                            currentImageIndex === index
                              ? "opacity-0"
                              : "opacity-100"
                          }
                              `}
                        />
                        <Image
                          src={item.filledIcon}
                          alt=" logo"
                          width={40}
                          height={40}
                          className={`absolute transition-opacity duration-1000  
                          ${
                            item.id == 0
                              ? "w-[40px] h-[40px]"
                              : item.id == 1
                              ? "w-[40px] h-[40px]"
                              : item.id == 2
                              ? "w-[40px] h-[40px]"
                              : "w-[40px] h-[40px]"
                          }
                              ${
                                currentImageIndex === index
                                  ? "opacity-100"
                                  : "opacity-0"
                              }`}
                        />
                      </div>
                    </div>
                  </div>
                  <div>
                    <LabelComponent
                      className={` text-[23px] font-bold text-white text-center flex ${
                        item.id == 0
                          ? " mx-[20px]"
                          : item.id == 1
                          ? ""
                          : item.id == 2
                          ? " mx-[20px]"
                          : " mx-[40px]"
                      }
                      `}
                      label={item.label}
                    />
                  </div>
                </div>
              </li>
            ))}
        </ul>
      </div>

      <motion.div className="xs:my-20 lg:my-40">
        <div className="flex justify-center mb-20">
          <h1 className="font-semibold text-4xl text-white">
            {Strings.SIMPLE_PRICING}
          </h1>
        </div>
        <div className="flex flex-wrap xs:gap-10 lg:gap-20 justify-center">
          {simplePricingData.map((item, index) => (
            <SimplePricing
              key={index}
              title={item.title}
              text={item.text}
              btntext={item.btntext}
              href={item.href}
              onBtnClick={item.onBtnClick}
            />
          ))}
        </div>
      </motion.div>

      <div className="text-center mx-[20px] md:mt-[-40px] xs:mt-[80px] bg-red-900- ">
        <LabelComponent
          label={Strings.DEFAULT_STATE}
          className=" font-semibold text-4xl text-transparent bg-clip-text bg-gradient-to-l from-contessa to-Tosca"
        />
        <p className=" font-extralight text-[19px]  mt-4 text-white ">
          {Strings.HOW_IT_WORKS_DESC}
        </p>
      </div>
      <div className=" bg-red-300- lg:mt-[100px] md:mt-[120px] xs:mt-[80px]">
        <CountComponent />
      </div>
      <div className="flex justify-center max-w-screen-2xl m-auto md:my-[80px] xs:my-[50px]">
        <ImageSliderComponent logos={logos} />
      </div>
      <div id="faq" className="text-center- mt-10- bg-yellow-900- ">
        {faqData?.length > 0 ? (
          <>
            <LabelComponent
              label={Strings.FAQS}
              className=" font-bold text-4xl text-white flex justify-center items-center"
            />
            <div className="xs:mt-[50px] space-y-6 justify-center flex flex-col items-center text-white">
              <AccordionWrapper data={faqData} />
            </div>
          </>
        ) : (
          <div></div>
        )}
      </div>
      <div className="flex justify-center md:my-[100px] xs:my-[50px] bg-red-900-">
        <div className="rounded-3xl md:rounded-[30px] flex justify-center mx-5 lg:mx-20 w-full max-w-screen-2xl m-auto bg-gradient-to-r from-tundora to-CodGray py-7 px-2 md:p-10">
          <div className="text-center">
            <h1 className="text-white font-bold text-xl md:text-4xl ">
              {Strings.HIRE_MANAGE}
            </h1>
            <h1 className="text-white font-extralight text-base md:text-2xl flex justify-center my-5 lg:my-10">
              {Strings.GET_A_FULLY_REMOTE}
            </h1>
            <button
              onClick={handleButtonClick}
              className="rounded-full bg-gradient-to-t from-contessa to-tosca hover:bg-gradient-to-b hover:from-Contessa hover:to-Tosca py-3 md:py-5 px-8 md:px-10 transition-transform hover:scale-110 transform duration-700"
            >
              <h1 className="font-light text-base md:text-2xl text-white">
                {Strings.HIRE_A_TOP_ENGINEER}
              </h1>
            </button>
          </div>
          <Hiretopengineer isOpen={isOpen} setIsOpen={setIsOpen} />
        </div>
      </div>
      <footer>
        <Footer />
      </footer>
    </div>
  );
};

export default Howitswork;
