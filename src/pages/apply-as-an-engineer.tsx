import "react-phone-input-2/lib/material.css";
import "react-phone-input-2/lib/style.css";
import "../app/globals.css";

import Applyasanengineer, { toggleModal1 } from "@/Component/Applyasanengineer";
import { Images, Strings } from "@/constant";
import { ReactNode, useEffect, useState } from "react";

import AccordionWrapper from "@/Component/AccordionWrapper";
import CircleRounded from "@/Component/CircleRounded";
import Cookie from "@/Component/cookies";
import DeveoperCard from "@/Component/DeveoperCard";
import { Footer } from "@/Component/footer";
import { GetServerSideProps } from "next";
import Image from "next/image";
import ImageSliderComponent from "@/Component/HomePage/ImageSlider";
import { LabelComponent } from "@/Component/label";
import NavbarComponent from "../Component/Navbar";
import React from "react";
import SliderApply from "../Component/SliderApply";
import axios from "axios";
import { motion } from "framer-motion";
import { trackPageView } from "@/utils/helper";
import { useInView } from "react-intersection-observer";

interface DeveloperData {
  role: ReactNode;
  fullName: ReactNode;
  hourlyRate: ReactNode;
  country: ReactNode;
  designation: ReactNode;
  firstName: ReactNode;
  profilePicture: string;
  engage?: {
    profilePicture: string;
    firstName: string;
    designation: string;
    country: string;
    hourlyRate: number;
  };
}

export const getServerSideProps: GetServerSideProps = async () => {
  try {
    const [developerResponse, faqResponse, successStoryResponse] =
      await Promise.all([
        axios.get(
          `${process.env.NEXT_PUBLIC_API_BASE_URL}home/getAvailableHireDeveloper`
        ),
        axios.get(
          `${process.env.NEXT_PUBLIC_API_BASE_URL}devdashboard/getFAQData?pageName=apply-as-an-engineer`
        ),
        axios.get(
          `${process.env.NEXT_PUBLIC_API_BASE_URL}home/getSuccessStoryDetails`
        ),
      ]);

    const developerData = developerResponse?.data?.developerData?.engage || [];
    const faqData = faqResponse?.data?.faqData || [];
    const successStoryDetailsData =
      successStoryResponse?.data?.successStoryDetailsData || [];

    return {
      props: {
        developerData,
        faqData,
        successStoryDetailsData,
      },
    };
  } catch (error) {
    console.error("Failed to fetch data:", error);
    return {
      props: {
        developerData: [],
        faqData: [],
        successStoryDetailsData: [],
      },
    };
  }
};

const FindADeveloperJob = ({
  logos,
  developerData,
  faqData,
  successStoryDetailsData,
}: any) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isOpen1, setIsOpen1] = useState(false);
  const [isAnimated, setIsAnimated] = useState(false);
  const [isSmallScreen, setIsSmallScreen] = useState(false);

  const { ref, inView } = useInView({
    triggerOnce: true,
  });

  React.useEffect(() => {
    if (inView) {
      setIsAnimated(true);
    }
  }, [inView]);

  const checkScreenSize = () => {
    setIsSmallScreen(window.innerWidth <= 768);
  };

  useEffect(() => {
    checkScreenSize();

    window.addEventListener("resize", checkScreenSize);

    return () => window.removeEventListener("resize", checkScreenSize);
  }, []);

  const storyTitle2 = {
    hideBottom: {
      opacity: 0,

      y: isSmallScreen ? 100 : 200,
    },
    showBottom: {
      opacity: 1,
      y: 0,
      transition: {
        ease: "easeInOut",
        duration: 1,
      },
    },
    exit: {
      opacity: 0,
      y: isSmallScreen ? -100 : -200,
      transition: {
        ease: "easeInOut",
        duration: 0.5,
      },
    },
  };

  useEffect(() => {
    trackPageView("/apply-as-an-engineer", "Apply As An Engineer Page");
  }, []);

  const imagesAndLabels = [
    {
      id: 0,
      image: Images.bussiness,
      label: Strings.ACCESS_TO_SILICON,
      filledIcon: Images.bussiness_fill,
    },
    {
      id: 1,
      image: Images.veify,
      label: Strings.VETTED_ONCE,
      filledIcon: Images.veify_fill,
    },
    {
      id: 2,
      image: Images.dollor,
      label: Strings.COMENTITIVE,
      filledIcon: Images.dollor_fill,
    },
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentImageIndex(
        (prevIndex) => (prevIndex + 1) % imagesAndLabels.length
      );
    }, 2000);

    return () => {
      clearInterval(timer);
    };
  }, [imagesAndLabels.length]);

  const Icon: React.FC<{ id: number; open: any }> = ({ id, open }) => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth={2}
      stroke="currentColor"
      className={`${
        id === open ? "rotate-180" : ""
      } h-5 w-5 transition-transform`}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M19.5 8.25l-7.5 7.5-7.5-7.5"
      />
    </svg>
  );

  const handleButtonClick = () => {
    toggleModal1(isOpen1, setIsOpen1);
  };

  return (
    <div className="overflow-hidden max-w-screen-2xl m-auto">
      <div className="">
        <Cookie />
      </div>
      <div className="bg-red-800-">
        <NavbarComponent />
      </div>
      <section className=" bg-green-900- flex justify-center items-center flex-col  bg-red-900- md:my-[100px] xs:my-[50px]">
        <h3 className="xl:text-[45px] lg:text-[35px] md:text-[30px] xs:text-[20px]  font-bold text  not-italic	">
          Work with top Silicon
        </h3>
        <h3 className="xl:text-[45px] lg:text-[35px] md:text-[30px] xs:text-[20px] font-bold text  not-italic	">
          Valley companies as a
        </h3>
        <h2 className="xl:text-[42px] lg:text-[33px] md:text-[28px] xs:text-[18px] font-bold text-white  not-italic	">
          remote engineer
        </h2>
        <p className="md:text-[18px] xs:text-[16px] text-center font-extralight text-white  leading-normal not-italic mt-[35px] xs:mx-4	">
          Define your own rate, get bi-weekly pay, and long term engagements
        </p>
        <button
          onClick={handleButtonClick}
          className=" mt-[40px] rounded-full bg-gradient-to-t from-contessa to-tosca  hover:bg-gradient-to-b hover:from-Contessa hover:to-Tosca  py-5 px-10 transition-transform hover:scale-110 transform duration-700"
        >
          <LabelComponent
            label="Apply as an Engineer"
            className=" font-light xs:text-xl lg:text-2xl text-white"
          />
        </button>
        <Applyasanengineer isOpen1={isOpen1} setIsOpen1={setIsOpen1} />
      </section>
      <section className="flex flex-wrap items-center justify-center gap-4 md:my-[100px] xs:my-[50px]">
        {developerData?.map((ele: DeveloperData, index: number) => (
          <motion.div
            key={index}
            initial="hideBottom"
            whileInView="showBottom"
            exit="exit"
            variants={storyTitle2}
            ref={ref}
            animate={isAnimated ? "showBottom" : "hideBottom"}
          >
            <DeveoperCard
              key={index}
              image={ele?.profilePicture}
              name={ele?.firstName}
              role={ele?.role}
              country={ele?.country}
              rate={ele?.hourlyRate}
            />
          </motion.div>
        ))}
      </section>
      <section className="flex justify-center lg:my-[100px]- bg-red-700- mb-[100px]- ">
        <ImageSliderComponent logos={logos} />
      </section>

      {/* <section className="flex justify-center">
        <div className="relative">
          <Image
            src={Images.laptop}
            width={1200}
            height={800}
            alt="/"
            className="w-full h-auto"
          />
          <video
            className="absolute top-[5%] left-[16%] w-[68%] h-[78%] rounded-md object-cover"
            controls
          >
            <source src={Images.videomp4} type="video/mp4" />
          </video>
        </div>
      </section> */}

      <section className="bg-red-700- md:my-[50px] xs:mt-[10px] ">
        <SliderApply successStoryDetailsData={successStoryDetailsData} />
      </section>

      <section className="xs:mt-40 lg:my-[100px] flex justify-center">
        <div className="rounded-3xl md:rounded-[30px] flex justify-center mx-5 lg:mx-20 w-full max-w-screen-2xl m-auto bg-gradient-to-r from-tundora to-CodGray py-7 px-2 md:p-10">
          <div className="text-center">
            <h1 className="font-bold text-xl md:text-4xl text-white ">
              Access to top silicon valley companies, certified <br />
              for life, and income stability.
            </h1>
            <button
              onClick={handleButtonClick}
              className="mt-7 md:mt-10 rounded-full bg-gradient-to-t from-contessa to-tosca  hover:bg-gradient-to-b hover:from-Contessa hover:to-Tosca py-5 px-10 transition-transform hover:scale-110 transform duration-700"
            >
              <LabelComponent
                label="Apply as an Engineer"
                className=" font-light text-xl md:text-2xl text-white"
              />
            </button>
          </div>
        </div>
      </section>
      <section className="lg:mt-[-80px]- md:mt-[-160px]- bg-yellow-800-">
        <CircleRounded />
      </section>

      <section className="flex flex-col justify-center xs:my-[60px]- bg-red-700- ">
        <div className="text-center xs:mt-[50px]- lg:mb-20- md:mb-[50px]- md:mt-[80px]- space-y-2 bg-gray-700-">
          <LabelComponent
            label={Strings.WHY_JOIN}
            className="text-white  font-semibold xs:text-2xl lg:text-4xl md:text-[30px]"
          />
          <div className=" font-semibold xs:text-2xl lg:text-4xl md:text-[30px] text-white">
            {/* <span className="text-contessa">e</span> */}
            eRemoteHire
          </div>
        </div>
        <div className="  flex xs:flex-col md:flex-row md:ml-6 md:mt-[100px] xs:mt-[50px]">
          <div>
            <div className="relative lg:ml-4 md:ml-0">
              <div
                style={{
                  backgroundImage: `url('${Images.circle}') `,
                  width: "",
                  backgroundSize: "contain",
                  backgroundPosition: "left",
                  backgroundRepeat: "no-repeat",
                  zIndex: "1",
                }}
                className="xs:hidden md:block animate-spin   xl:h-[650px] xl:w-[650px] lg:h-[500px] lg:w-[500px]  md:h-[400px] md:w-[400px]  "
              ></div>
              <div>
                <div className="xs:flex xs:justify-center xs:items-center xs:flex-col xs:w-full  text-center">
                  <div className="text-white  xl:text-[50px] md:text-[40px]  xs:text-[40px] leading-normal not-italic font-bold md:absolute  xl:left-[110px] lg:left-[47px] xl:top-[7rem] md:top-[3rem]  md:left-[47px] ">
                    <div className="relative  xl:h-[430px] xl:w-[430px] lg:h-[400px] lg:w-[400px] md:h-[300px] md:w-[300px] xs:h-[270px] xs:w-[270px] bg-gradient-to-l from-[#481e1f]- to-[#6b443a]- rounded-full flex justify-center items-center animate-gradient">
                      {/* <Image
                        src={Images.ellipse_men}
                        alt="/"
                        height={150}
                        width={350}
                        className="xs:h-[200px] xs:w-[200px] md:h-[250px] md:w-[250px]  lg:h-[320px] lg:w-[320px] "
                      /> */}
                      {/* <div className="absolute xs:bottom-[-55px] lg:bottom-[-60px]">
                        <Image
                          src={Images.Play_icon}
                          alt="/"
                          height={90}
                          width={90}
                          className="text-center z-10 xs:h-[50px] w-[50px] lg:h-[90px] lg:w-[90px]"
                        />
                      </div> */}
                    </div>
                  </div>
                </div>
              </div>

              <div className="xs:hidden md:block ">
                <div className="absolute xl:top-[141px] xl:right-[30px] lg:top-[55px] lg:right-[55px] md:top-[32px] md:right-[47px]">
                  <Image
                    src={Images.gruop}
                    alt="animated logo"
                    width={35}
                    height={35}
                    className=""
                  />
                </div>
                <div className="absolute xl:top-[19rem] xl:right-[-1rem]  lg:top-[13rem] lg:right-[-1rem] md:top-[10rem] md:right-[-1rem]">
                  <Image
                    src={Images.gruop}
                    alt="animated logo"
                    width={35}
                    height={35}
                    className=""
                  />
                </div>
                <div className="absolute xl:top-[29rem] xl:right-[22px] lg:top-[23rem] lg:right-[23px] md:top-[19rem] md:right-[21px]">
                  <Image
                    src={Images.gruop}
                    alt="animated logo"
                    width={35}
                    height={35}
                    className=""
                  />
                </div>
                {/* <div>
                    <Image
                      src={Images.gruop}
                      alt="animated logo"
                      width={30}
                      height={30}
                      className="absolute xl:top-[34rem] xl:right-[81px] lg:top-[24rem] lg:right-[39px] md:top-[19rem] md:right-[23px]"
                    />
                  </div> */}
              </div>
            </div>
          </div>
          <div className="xs:mt-[70px] lg:mt-[28px] md:ml-10 bg-pink-900-">
            {imagesAndLabels.map((item, index) => (
              <div key={index} className="flex ">
                <div
                  className={`flex ${
                    item.id == 0
                      ? " w-full flex xs:flex-col md:flex-row items-center xl:mt-[87px] lg:mt-[0px] md:mt-[-54px] xl:ml-[1px] lg:ml-[0rem] md:ml-[0px]    xs:mt-0 xs:ml-0 xs:w-full"
                      : item.id == 1
                      ? "flex xs:flex-col md:flex-row items-center xl:mt-[68px] lg:mt-[60px]  md:mt-[48px] xl:ml-[40px] lg:ml-[4rem] md:ml-[2rem] xs:mt-0 xs:ml-0 xs:w-full"
                      : item.id == 2
                      ? "flex xs:flex-col md:flex-row items-center xl:mt-[78px] lg:mt-[70px] md:mt-[60px] xl:ml-[-1px] lg:ml-[0rem] md:ml-[-20px]    xs:mt-0 xs:ml-0 xs:w-full"
                      : "flex xs:flex-col md:flex-row items-center xl:mt-[50px]  lg:mt-[50px]   md:mt-[30px] xl:ml-[0rem]  lg:ml-[0rem] md:ml-[2rem]  xs:mt-0 xs:ml-0 xs:w-full"
                  }`}
                >
                  <div>
                    <div className="border-2 border-Contessa rounded-full overflow-hidden lg:py-[23px] lg:px-[23px] md:py-[18px] md:px-[18px] xs:py-[18px] xs:px-[18px]">
                      <div className="relative rounded-full w-[40px] h-[40px]">
                        <div className={`rounded-full overflow-hidden }`}>
                          <div className="absolute">
                            <Image
                              src={item.image}
                              alt="animated logo"
                              width={40}
                              height={40}
                              className={` transition-opacity duration-1000 ${
                                item.id == 0
                                  ? "w-[40px] h-[40px]"
                                  : item.id == 1
                                  ? "w-[40px] h-[40px]"
                                  : item.id == 2
                                  ? "w-[40px] h-[40px]"
                                  : "w-[40px] h-[40px]"
                              } ${
                                currentImageIndex === index
                                  ? "opacity-0"
                                  : "opacity-100"
                              }
                              `}
                            />
                          </div>
                          <div className="absolute">
                            <Image
                              src={item.filledIcon}
                              alt="animated logo"
                              width={40}
                              height={40}
                              className={` transition-opacity duration-1000  ${
                                item.id == 0
                                  ? "w-[40px] h-[40px]"
                                  : item.id == 1
                                  ? "w-[40px] h-[40px]"
                                  : item.id == 2
                                  ? "w-[40px] h-[40px]"
                                  : "w-[40px] h-[40px]"
                              }
                              ${
                                currentImageIndex === index
                                  ? "opacity-100"
                                  : "opacity-0"
                              }`}
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="xl:ml-[2rem] lg:ml-[2rem] md:ml-[10px] xs:ml-0 xs:mt-[10px]  md:mt-0 md:text-[16px] lg:text-[20px] xl:text-[28px] xs:text-[18px] xs:pb-6 md:pb-0  text-white  font-light not-italic">
                    <LabelComponent label={item.label} />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
      {/* <div className="text-center">
        <button
          onClick={openModal}
          className="mt-[70px] rounded-full bg-gradient-to-t from-tosca to-contessa py-5 px-10 hover:from-gray-950 hover:to-CodGray"
        >
          <LabelComponent
            label={Strings.APPLY_AS_AN_ENGINEER}
            className=" font-light xs:text-xl lg:text-2xl text-white"
          />
        </button>
      </div> */}
      <section>
        {/* <div className="flex justify-center mt-[4rem]">
          <div className=" text-center xs:px-7 lg:px-1 border-transparent rounded-[30px] xs:h-[307px] sm:h-[270px] md:h-[280px] lg:h-[307px] xs:w-[330px] sm:w-[600px] md:w-[750px] lg:w-[950px] xl:w-[1200px] bg-gradient-to-r from-tundora to-CodGray mb-20 pt-10">
            <div className="text-center mb-7">
              <LabelComponent
                label={Strings.FIXED_SALARY}
                className=" font-bold xs:text-3xl lg:text-4xl text-white "
              />
            </div>
            <p className=" font-extralight text-lg text-white">
              We pay you what you believe is fair. Pay is a fixed monthly
              <br></br>salary and is paid bi-weekly.
            </p>
            <button
              onClick={openModal}
              className="xs:mt-[26px] sm:mt-[20px] lg:mt-[36px] rounded-full bg-gradient-to-t from-tosca to-contessa py-5 px-10 hover:from-gray-950 hover:to-CodGray"
            >
              <LabelComponent
                label={Strings.APPLY_AS_AN_ENGINEER}
                className=" font-light xs:text-xl lg:text-2xl text-white"
              />
            </button>
          </div>
        </div> */}
      </section>
      <div className="text-center- md:my-[100px] xs:my-[50px] ">
        {faqData?.length > 0 ? (
          <>
            <LabelComponent
              label={Strings.FAQS}
              className=" font-bold text-4xl text-white flex justify-center items-center"
            />
            <div className="xs:mt-[50px] space-y-6 justify-center flex flex-col items-center text-white">
              <AccordionWrapper data={faqData} />
            </div>
          </>
        ) : (
          <div></div>
        )}
      </div>

      {/* <div className="flex justify-center mt-28">
        <div className=" text-center px-1 border-transparent rounded-[30px] xs:h-[307px] sm:h-[250px] md:h-[280px] lg:h-[307px] xs:w-[330px] sm:w-[600px] md:w-[750px] lg:w-[950px] xl:w-[1200px] bg-gradient-to-r from-tundora to-CodGray mb-20 pt-10">
          <div className="text-center mb-6">
            <LabelComponent
              label={Strings.APPLY_AS_AN_ENGINEER}
              className=" font-bold xs:text-3xl lg:text-4xl "
            />
          </div>
          <p className=" font-extralight text-lg">
            Get a Remote Engineer Position with Silicon Valley Companies
          </p>
          <button
            onClick={openModal}
            className="xs:mt-[50px] sm:mt-[30px] md:mt-[40px] lg:mt-[50px] rounded-full bg-gradient-to-t from-tosca to-contessa py-5 px-10 hover:from-gray-950 hover:to-CodGray"
          >
            <LabelComponent
              label={Strings.APPLY_AS_AN_ENGINEER}
              className=" font-light xs:text-xl lg:text-2xl text-white"
            />
          </button>
        </div>
      </div> */}

      <footer>
        <Footer />
      </footer>
    </div>
  );
};

export default FindADeveloperJob;
