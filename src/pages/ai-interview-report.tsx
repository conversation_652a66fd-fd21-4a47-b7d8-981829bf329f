import "../app/globals.css";

import {
  ArrowRightOutlined,
  CopyOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  DownOutlined,
  DownloadOutlined,
  MenuOutlined,
  SwapRightOutlined,
} from "@ant-design/icons";
import { Images, Strings } from "@/constant";
import React, { useEffect, useRef, useState } from "react";

import { ArrowRightCircleIcon } from "@heroicons/react/24/outline";
import Image from "next/image";
import html2canvas from "html2canvas";
import { jsPDF } from "jspdf";
import { useRouter } from "next/router";

const AiInterviewReport = () => {
  const router = useRouter();
  const { skill_rating, proctoring_score, feedback } = router.query;
  const [activeTab, setActiveTab] = useState("ai-interview-result");
  const [isPopoverOpen, setIsPopoverOpen] = useState(false);
  const popoverRef = useRef<HTMLDivElement | null>(null);
  const [transcript, setTranscript] = useState(false);
  const [task, setTask] = useState(false);

  const reportRef = useRef<HTMLDivElement | null>(null);

  const [isPlaying, setIsPlaying] = useState(false);
  const videoRef = useRef<HTMLVideoElement | null>(null);
  const [videoURL, setVideoURL] = useState<string | null>(null);

  useEffect(() => {
    const storedVideo = localStorage.getItem("recordedVideo");
    if (storedVideo) {
      setVideoURL(storedVideo);
    }
  }, []);

  const handlePlay = () => {
    if (videoRef.current) {
      videoRef.current.play();
      setIsPlaying(true);
    }
  };

  const handlePause = () => {
    setIsPlaying(false);
  };

  const handleDownloadVideo = () => {
    if (!videoURL) return;

    const link = document.createElement("a");
    link.href = videoURL;
    link.download = "AI_Interview_Video.webm";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleImageClick = () => {
    setIsPopoverOpen((prev) => !prev);
  };

  const handleCopyLink = () => {
    navigator.clipboard.writeText(window.location.href);
    alert("Link copied to clipboard!");
    setIsPopoverOpen(false);
  };

  const handleDownloadReport = async () => {
    if (!reportRef.current) return;

    const element = reportRef.current;

    try {
      const canvas = await html2canvas(element, { scale: 2, useCORS: true });
      const imgData = canvas.toDataURL("image/png");
      const pdf = new jsPDF("p", "mm", "a4");

      const pageWidth = pdf.internal.pageSize.getWidth();
      const pageHeight = pdf.internal.pageSize.getHeight();

      const imgWidth = pageWidth;
      const imgHeight = (canvas.height * imgWidth) / canvas.width;

      let position = 0;

      pdf.addImage(imgData, "PNG", 0, position, imgWidth, imgHeight);

      while (position + pageHeight < imgHeight) {
        position += pageHeight;
        pdf.addPage();
        pdf.addImage(imgData, "PNG", 0, -position, imgWidth, imgHeight);
      }

      pdf.save(`Report.pdf`);
      setIsPopoverOpen(false);
    } catch (error) {
      console.error("Error while generating PDF", error);
    }
  };
  const handleTabClick = (tab: string) => {
    setActiveTab(tab);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        popoverRef.current &&
        !popoverRef.current.contains(event.target as Node)
      ) {
        setIsPopoverOpen(false);
      }
    };

    if (isPopoverOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isPopoverOpen]);

  const toggleDropdown = () => {
    setTranscript((prev) => !prev);
  };

  const toggleDropdown2 = () => {
    setTask((prev) => !prev);
  };

  const skillImages: Record<string, string> = {
    AWS: "/ProgrammingSkills/aws.svg",
    AWSWHITE: "/ProgrammingSkills/awswhite.svg",
    ATOM: "/ProgrammingSkills/atom.png",
    BLOCKCHAIN: "/ProgrammingSkills/blockchain.png",
    REACTNATIVE: "/ProgrammingSkills/react-native.svg",
    PYTHON: "/ProgrammingSkills/python.svg",
    JAVASCRIPT: "/ProgrammingSkills/javascript.svg",
    FIGMA: "/ProgrammingSkills/figma.svg",
    REACTJS: "/ProgrammingSkills/reactjs.svg",
    NODEJS: "/ProgrammingSkills/node-js.svg",
    NESTJS: "/ProgrammingSkills/nestjs.svg",
    NEXTJS: "/ProgrammingSkills/nextjs.svg",
    TYPESCRIPT: "/ProgrammingSkills/typescript.svg",
    JAVA: "/ProgrammingSkills/java.svg",
    HTML: "/ProgrammingSkills/html5.svg",
    CSS: "/ProgrammingSkills/css3.svg",
    ANDROID: "/ProgrammingSkills/android-os.svg",
    ANGULARJS: "/ProgrammingSkills/angularjs.svg",
    IOS: "/ProgrammingSkills/ios.svg",
    SWIFT: "/ProgrammingSkills/swift.svg",
    TAILWINDCSS: "/ProgrammingSkills/tailwind-css.svg",
    VUE: "/ProgrammingSkills/vue.svg",
    UNITY: "/ProgrammingSkills/unity.svg",
    DEFAULTSKILL: "/ProgrammingSkills/defaultskill.svg",
  };

  const interviewResults = [
    { skill: "NEXTJS", level: "Experienced" },
    { skill: "JAVASCRIPT", level: "Good" },
    { skill: "TYPESCRIPT", level: "Experienced" },
    { skill: "HTML", level: "Experienced" },
    { skill: "Coding Exercises", level: "Experienced" },
    { skill: "Soft Skills", level: "Experienced" },
  ];
  const levelStyles: Record<string, string> = {
    "Not experienced": "text-red-500 bg-red-100",
    Good: "text-yellow-500 bg-yellow-100",
    Experienced: "text-green-500 bg-[#daffdd]",
    Senior: "text-blue-500 bg-blue-100",
  };

  const interviewQA = [
    {
      question:
        "Hello! I am eRemoteHire's AI interviewer. Welcome, I'm excited to get to know you. Please introduce yourself?",
      answer:
        "I am Ayush Mittal and I am working at Société Générale for more than two years now. I am an IIT Kharagpur graduate and I am currently working as a software engineer in the machine learning team where I build and productionize multiple ML solutions for various business and front desk users.",
    },
    {
      question:
        "Explain the core differences between client-side rendering (CSR) and server-side rendering (SSR), and how Next.js leverages these techniques. When would you choose one over the other?",
      answer:
        "CSR renders content in the browser after fetching JavaScript, while SSR pre-renders pages on the server before sending them to the browser. Next.js allows both; CSR is useful for dynamic, highly interactive pages, while SSR is ideal for SEO and pre-fetched data.",
    },
    {
      question:
        "Describe how you would implement dynamic routing in a Next.js application using both the pages and the app directory. Explain the differences between the two approaches and where you would prefer one over the other?",
      answer:
        "In the 'pages' directory, dynamic routes are handled using `[id].tsx` inside `pages/`. In the 'app' directory (using the new App Router), routing is more component-driven, using folder-based structure. The App Router is preferred for better flexibility with React Server Components.",
    },
    {
      question:
        "Explain the differences between getServerSideProps, getStaticProps, and getStaticPaths in Next.js. Provide a scenario where each would be the best choice for fetching data.",
      answer:
        "getServerSideProps fetches data on every request, useful for real-time content. getStaticProps pre-fetches at build time, best for static content like blogs. getStaticPaths is used with getStaticProps for dynamic static pages.",
    },
    {
      question:
        "You're experiencing slow load times for a specific page in your Next.js application. Describe the steps you would take to diagnose and address this performance issue. What Next.js-specific tools or techniques would you consider?",
      answer:
        "I would analyze the page using Next.js Analytics and Chrome DevTools, optimize large images, reduce bundle size with dynamic imports, and consider caching. If needed, I'd use ISR (Incremental Static Regeneration) for optimizing server loads.",
    },
  ];

  const score = Array.isArray(proctoring_score)
    ? parseInt(proctoring_score[0])
    : parseInt(proctoring_score || "0");

  const parsedFeedback =
    typeof feedback === "string" ? JSON.parse(feedback) : feedback;

  return (
    <div
      ref={reportRef}
      style={{
        backgroundColor: "#f5f4f8",
        fontFamily: "outfit",
      }}
    >
      <div className="mx-5 lg:mx-[70px] max-w-screen-2xl m-auto">
        <div className="flex justify-between items-center py-10">
          <h1 className="text-3xl font-bold text-black">Candidate’s details</h1>
          <div className="relative inline-block">
            <Image
              src="/images/threedots.svg"
              alt="Options"
              width={40}
              height={40}
              className="h-10 w-10 rounded-md border cursor-pointer"
              onClick={handleImageClick}
            />
            {isPopoverOpen && (
              <div
                ref={popoverRef}
                className="absolute font-normal text-sm top-12 right-2 w-max bg-white border shadow-lg rounded-md p-2 z-10"
              >
                <button
                  onClick={handleCopyLink}
                  className="block w-full text-left text-gray-700 p-2 hover:bg-gray-100"
                >
                  <CopyOutlined /> Copy Link
                </button>
                <button
                  onClick={handleDownloadReport}
                  className="block w-full text-left text-gray-700 p-2 hover:bg-gray-100"
                >
                  <DownloadOutlined /> Download Report pdf
                </button>
              </div>
            )}
          </div>
        </div>
        <section className="w-full bg-white p-5 rounded-lg">
          <div className="flex items-center gap-x-5">
            <Image
              src={Images.ellipse_men}
              alt={""}
              width={500}
              height={500}
              className="h-20 w-20 rounded-full"
            />
            <div className="space-y-2 w-full">
              <h1 className="font-bold text-black text-2xl">John</h1>
              <div className="flex items-center gap-x-2">
                <h1 className="bg-tosca bg-opacity-30 text-tosca font-normal text-sm p-2 rounded-md">
                  Software Engineer
                </h1>
                <h1 className="bg-tosca bg-opacity-30 text-tosca font-normal text-sm p-2 rounded-md">
                  LinkedIn profile
                </h1>
              </div>
              <div className="flex justify-between items-center w-full">
                <div className="flex items-center gap-x-2">
                  <h1 className="bg-opacity-30 border text-black font-normal text-sm p-2 rounded-md">
                    India
                  </h1>
                  <h1 className="bg-opacity-30 border text-black font-normal text-sm p-2 rounded-md">
                    <EMAIL>
                  </h1>
                  <h1 className="bg-opacity-30 border text-black font-normal text-sm p-2 rounded-md">
                    +************
                  </h1>
                </div>
                <p className="text-black font-normal text-xs">
                  AI interview completed on Dec 22, 2024
                </p>
              </div>
            </div>
          </div>
        </section>
        {/* <section className="text-black my-5">
          <div className="flex w-max items-center border bg-[#e9e3e3] p-1 rounded-lg text-black space-x-4 ">
            <button
              onClick={() => handleTabClick("ai-interview-result")}
              className={`p-2 ${activeTab === "ai-interview-result" ? "bg-white rounded-md" : ""
                }`}
            >
              AI Interview Result
            </button>
            <button
              onClick={() => handleTabClick("about-aayush")}
              className={`p-2 ${activeTab === "about-aayush" ? "bg-white rounded-md" : ""
                }`}
            >
              About John
            </button>
            <button
              onClick={() => handleTabClick("experience")}
              className={`p-2 ${activeTab === "experience" ? "bg-white rounded-md" : ""
                }`}
            >
              Experience
            </button>
            <button
              onClick={() => handleTabClick("notes")}
              className={`p-2 ${activeTab === "notes" ? "bg-white rounded-md" : ""
                }`}
            >
              Notes
            </button>
          </div>
          <div className="w-full mt-5">
            {activeTab === "ai-interview-result" && (
              <div className="p-5 space-y-5 bg-white rounded-lg text-black">
                <h1 className="font-semibold text-xl">
                  Interview result summary
                </h1>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {interviewResults.map((result, index) => {
                    const skillImage =
                      skillImages[result.skill] || skillImages.DEFAULTSKILL;
                    const levelStyle =
                      levelStyles[result.level] || "text-gray-500 bg-gray-100";
                    return (
                      <div
                        key={index}
                        className="border rounded-lg w-full p-5 flex justify-between items-center"
                      >
                        <div className="flex items-center gap-x-2">
                          <Image
                            src={skillImage}
                            alt={result.skill}
                            width={700}
                            height={700}
                            className="h-10 w-10"
                          />
                          <p className="font-normal text-base">
                            {result.skill}
                          </p>
                        </div>
                        <div className={`p-1 rounded-md ${levelStyle}`}>
                          {result.level}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}
            {activeTab === "about-aayush" && (
              <div className="p-5 bg-white rounded-lg">
                <h3>About John</h3>
                <p>Information about John will be displayed here.</p>
              </div>
            )}
            {activeTab === "experience" && (
              <div className="p-5 bg-white rounded-lg">
                <h3>Experience</h3>
                <p>Details about experience will be displayed here.</p>
              </div>
            )}
            {activeTab === "notes" && (
              <div className="p-5 bg-white rounded-lg">
                <h3>Notes</h3>
                <p>Additional notes will be displayed here.</p>
              </div>
            )}
          </div>
        </section> */}
        <section className="my-5">
          <div className="p-5 space-y-5 bg-white rounded-lg text-black">
            <div className="flex items-center justify-between">
              <h1 className="font-semibold text-xl">AI interview recording</h1>
              <p
                className="text-black font-normal gap-x-1 text-sm cursor-pointer flex items-center"
                onClick={handleDownloadVideo}
              >
                <DownloadOutlined /> Download video
              </p>
            </div>
            <div className="videoreport">
              <div className="bg-tosca bg-opacity-30 p-2 rounded-xl relative">
                <div className="bg-black p-2 lg:p-4 rounded-xl">
                  {videoURL ? (
                    <video
                      ref={videoRef}
                      className="object-cover rounded-xl w-full"
                      controls
                      onPause={handlePause}
                      onPlay={() => setIsPlaying(true)}
                    >
                      <source src={videoURL} type="video/mp4" />
                    </video>
                  ) : (
                    <p className="text-gray-500 mt-5">
                      No recording available.
                    </p>
                  )}
                </div>
                {!isPlaying && videoURL && (
                  <Image
                    src={Images.Play_icon}
                    alt="Play Icon"
                    height={80}
                    width={80}
                    onClick={handlePlay}
                    className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 cursor-pointer xs:hidden lg:flex h-20 w-20 bg-gradient-to-l from-[#481e1f] to-[#6b443a] rounded-full"
                  />
                )}
              </div>
            </div>
            <div className="w-full">
              <div
                className={`flex ${
                  transcript
                    ? "border-x border-t rounded-t-lg"
                    : "border rounded-lg"
                } p-4 items-center justify-between cursor-pointer`}
                onClick={toggleDropdown}
              >
                <h1 className="font-semibold text-lg">Feedback</h1>
                <DownOutlined
                  className={`transition-transform duration-300 ease-in-out ${
                    transcript ? "rotate-180" : "duration-300"
                  }`}
                />
              </div>
              {transcript && (
                <div
                  className={`border font-medium text-sm text-justify bg-white p-4 space-y-8 w-full overflow-y-scroll custom-scrollbar rounded-b-lg transition-all duration-300 ease-in-out ${
                    transcript ? "max-h-[383px]" : "max-h-0"
                  }`}
                >
                  <h1
                    dangerouslySetInnerHTML={{
                      __html: (Array.isArray(parsedFeedback)
                        ? parsedFeedback.join(" ")
                        : parsedFeedback
                      ).replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>"),
                    }}
                    className=""
                  ></h1>
                </div>
              )}
            </div>
          </div>
        </section>
        <section className="my-5">
          <div className="text-black font-semibold bg-white w-max px-4 py-2 rounded-lg flex items-center gap-x-2">
            AI rating :
            <p
              className={`p-1 rounded-md ${
                skill_rating === "Beginner"
                  ? "text-red-600 bg-red-200"
                  : skill_rating === "Intermediate"
                  ? "text-[#CB9620] bg-[#ECE2C1]"
                  : "text-green-500 bg-[#daffdd]"
              }`}
            >
              {skill_rating}
            </p>
          </div>
        </section>
        <section className="pb-5">
          <div className="bg-white p-5 rounded-lg w-full text-black space-y-3">
            <h1 className="font-semibold text-xl">Proctoring result</h1>
            <p className="font-normal text-sm text-[#2A2B2F]">
              This score is between O & 100% which is calculated using the
              duration of the violations that relate to cheating. For example,
              tab movements, eye movements, and more. The higher the score, the
              better.
            </p>
            {/* <div>
              <h1>
                <span className="font-semibold">Tab Switching:</span> 3 tab
                switches detected, indicating multitasking or external searches.
              </h1>
              <h1>
                <span className="font-semibold">Eye Movement:</span> The
                candidate looked away from the screen 50+ times, each for a
                significant duration.
              </h1>
            </div> */}
            {/* <div className="bg-[#ECE2C1] w-max h-12 flex items-center px-2 rounded-lg font-bold text-xl text-black">
              Final proctoring score:
              <span className="text-[#CB9620] ml-2">{proctoring_score}</span>
              
            </div> */}
            <div
              className={`w-max h-12 flex items-center px-2 rounded-lg font-bold text-xl text-black ${
                score <= 50
                  ? "bg-red-200"
                  : score <= 70
                  ? "bg-[#ECE2C1]"
                  : "bg-[#daffdd]"
              }`}
            >
              Final proctoring score:
              <span
                className={`ml-2 ${
                  score <= 50
                    ? "text-red-600"
                    : score <= 70
                    ? "text-[#CB9620]"
                    : "text-green-600"
                }`}
              >
                {proctoring_score}%
              </span>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
};

export default AiInterviewReport;
