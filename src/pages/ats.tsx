import "../app/globals.css";

import React, { useEffect, useState } from "react";

import Swal from "sweetalert2";
import axios from "axios";

interface ResponseData {
    error?: string;
    [key: string]: any;
}
const ResumeScanner = () => {
    const [jobDescription, setJobDescription] = useState<string>("");
    const [files, setFiles] = useState<File[]>([]);
    const [response, setResponse] = useState<ResponseData | null>(null);
    const [option, setOption] = useState<string>("percentage_match");
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [filesChanged, setFilesChanged] = useState<boolean>(false);
    const [isFocused, setIsFocused] = useState<boolean>(false);

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const uploadedFiles = Array.from(event.target.files || []);

        
        if (files.length > 0) {
            Swal.fire({
                icon: "error",
                title: "Multiple Files Not Allowed",
                text: "You can only upload one file.",
            });

            event.target.value = "";
            return;
        }

        const validFiles = uploadedFiles.filter((file) => {
            if (file.type !== "application/pdf") {
                Swal.fire({
                    icon: "error",
                    title: "Invalid file type",
                    text: `${file.name} is not a PDF file. Please upload a PDF file.`,
                });
                return false;
            }
            if (file.size > 10 * 1024 * 1024) {
                Swal.fire({
                    icon: "error",
                    title: "File too large",
                    text: `${file.name} is too large. Please upload a file smaller than 10MB.`,
                });
                return false;
            }
            return true;
        });

      
        setFiles(validFiles);
        setFilesChanged(true);

  
        event.target.value = "";
    };


    const handleRemoveFile = (indexToRemove: any) => {
        setFiles((prevFiles) =>
            prevFiles.filter((_, index) => index !== indexToRemove)
        );
        setFilesChanged(true);

        const fileInput = document.getElementById("fileInput") as HTMLInputElement;
        if (fileInput) {
            fileInput.value = "";
        }
    };

    const openFilePicker = () => {
        const fileInput = document.getElementById("fileInput");
        if (fileInput) {
            fileInput.click();
        }
    };

    const handleSubmit = async (event: any) => {
        event.preventDefault();
      
        const isGibberish = (text: string) => {
          const words = text.trim().split(/\s+/);
          const longWords = words.filter((word) => word.length > 3);
          return longWords.length < 3 || text.length < 10;
        };
      
        if (!jobDescription || isGibberish(jobDescription)) {
          Swal.fire({
            icon: "error",
            title: "Invalid Job Description",
            text: "Please provide a valid job description.",
          });
          return;
        }
      
        if (files.length === 0) {
          Swal.fire({
            icon: "error",
            title: "No Resume Uploaded",
            text: "Please upload your resume before submitting.",
          });
          return;
        }
      
        const formData = new FormData();
        files.forEach((file) => formData.append("files", file));
        formData.append("job_description", jobDescription);
        formData.append("option", option);
      
        setIsLoading(true);
      
        try {
          const res = await axios.post(
            `${process.env.NEXT_PUBLIC_API_BASE_URL}ats/add`,
            formData,
            {
              headers: {
                "Content-Type": "multipart/form-data",
              },
            }
          );
      
          const resData = res.data;
      
         
          if (resData.error) {
            if (resData.error.toLowerCase().includes("job description")) {
              Swal.fire({
                icon: "error",
                title: "Invalid Job Description",
                text: resData.error, 
              });
            } else {
              Swal.fire({
                icon: "error",
                title: "Error",
                text: resData.error,
              });
            }
      
            setResponse({
              error: resData.error,
            });
            return;
          }
      
       
          const invalidFileEntry = (resData.results || []).find((entry: any) => entry.error);
      
          if (invalidFileEntry) {
            Swal.fire({
              icon: "error",
              title: "Invalid File Uploaded",
              text: `The uploaded document '${invalidFileEntry.file_name}' is not a resume.`,
            });
      
            setResponse({
              error: invalidFileEntry.error,
              results: resData.results,
            });
            return;
          }
      
       
          const aiScore = resData.percentage;
      
          Swal.fire({
            icon: "success",
            title: "Success",
            text: "Resume evaluated successfully!",
          });
      
          setResponse({
            ...resData,
            score: aiScore,
          });
      
          setFilesChanged(false);
      
        } catch (error: any) {
          if (axios.isAxiosError(error)) {
            if (error.response) {
              const backendError =
                error.response.data?.detail ||
                error.response.data?.error ||
                "An error occurred while processing the request.";
      
              Swal.fire({
                icon: "error",
                title: `Error ${error.response.status}`,
                text: backendError,
              });
      
              setResponse({
                error: backendError,
              });
      
            } else if (error.request) {
              Swal.fire({
                icon: "error",
                title: "Network Error",
                text: "No response from the server. Please check your network connection.",
              });
      
              setResponse({
                error: "No response from the server.",
              });
      
            } else {
              Swal.fire({
                icon: "error",
                title: "Request Error",
                text: error.message,
              });
      
              setResponse({
                error: error.message,
              });
            }
      
          } else {
            Swal.fire({
              icon: "error",
              title: "Unexpected Error",
              text: "An unknown error occurred. Please try again later.",
            });
      
            setResponse({
              error: "An unknown error occurred.",
            });
          }
        } finally {
          setIsLoading(false);
        }
      };
      
      



    const handleJobDescriptionChange = (event: any) => {
        setJobDescription(event.target.value);
        event.target.style.height = "auto";
        event.target.style.height = `${event.target.scrollHeight}px`;
    };

    useEffect(() => {
        if (response) {
            Swal.fire({
                title: "Are you sure?",
                text: "Switching the evaluation type will reset the current response.",
                icon: "warning",
                showCancelButton: true,
                confirmButtonText: "Yes, reset it!",
                cancelButtonText: "No, keep it",
                customClass: {
                    confirmButton: "swal-confirm-btn",
                    cancelButton: "swal-cancel-btn"
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    setResponse(null);
                    setFilesChanged(true);
                } else {
                    setOption((prevOption) => prevOption);
                }
            });
        }
    }, [files.length, option]);

    useEffect(() => {
        
        document.body.style.backgroundColor = "black";
        document.body.style.color = "#e0e0e0"; 
    }, []);

    const renderResponse = () => {
        if (!response) {
            return null;
        }

        if (response.error) {
            Swal.fire({
                icon: "error",
                title: "Error",
                text: response.error,
            });
            return (
                <p style={{ color: "#f3f3f3" }}>
                    {response.error}
                </p>
            );
        }

        const percentage_score = response?.results || [];

       
        const invalidFileEntry = percentage_score.find((entry: { error?: string }) => entry.error);

        if (invalidFileEntry) {
            Swal.fire({
                icon: "error",
                title: "Invalid File Uploaded",
                text: `The uploaded document '${invalidFileEntry.file_name}' is not a resume.`,
            });
            return null; 
        }

    
        const getScoreColor = (score: number) => {
            if (score >= 80) {
                return "lightgreen";
            } else if (score >= 50) {
                return "yellow";
            } else {
                return "red";
            }
        };

        return (
            <div>
                {response?.message && (
                    <h3
                        style={{
                            color: "#eaeaea",
                            fontSize: "24px",
                            fontWeight: "bold",
                            marginBottom: "10px",
                            fontFamily: "'Outfit', sans-serif",
                        }}
                    >
                        {response.message}
                    </h3>
                )}

                {percentage_score.length > 0 && (
                    <div className="evaluation-wrapper">
                        {percentage_score.map((entry: any, index: number) => (
                            <div
                                key={index}
                                className={`evaluation-entry ${index > 0 ? "with-border" : ""}`}
                            >
                                <h5 className="evaluation-file-title">{entry?.file_name}</h5>
                                <div
                                    style={{
                                        backgroundColor: getScoreColor(entry.percentage_score),
                                    }}
                                    className="flex items-center font-semibold text-black gap-x-1 w-max text-base px-2 py-1 rounded-md"
                                >
                                    <h1 className="text-black">Percentage score:</h1>
                                    <h1>
                                        {entry.percentage_score || 0}%
                                    </h1>
                                </div>
                            </div>
                        ))}
                    </div>
                )}
            </div>
        );
    };




    return (
        <div className="resume-container">
            {isLoading && (
                <div className="loading-overlay">
                    <div className="dot-loader">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                    <p className="text-white mt-4 text-lg">Analyzing your Resume...</p>
                </div>
            )}

            <h1 className="header-title">ATS System</h1>

            <form onSubmit={handleSubmit}>
                <div className="form-group">
                    <label className="form-label">Enter Job Description:</label>

                  
                    {jobDescription === "" && !isFocused && (
                        <p className={`text-gray-500`}>
                            Please provide a structured job description so that the resume score would be accurately calculated.
                        </p>
                    )}

                    <textarea
                        className="textarea"
                        value={jobDescription}
                        onChange={handleJobDescriptionChange}
                        onFocus={() => setIsFocused(true)}  
                        onBlur={() => setIsFocused(jobDescription !== "")} 
                        title="Job Description"
                    ></textarea>
                </div>



                <div className="form-group">
                    <div className="flex items-center gap-2">
                        <label className="form-label">Upload your Resumes (PDF):</label>
                        <button type="button" onClick={openFilePicker} className="button">
                            Browse Files
                        </button>
                    </div>

                    <input
                        id="fileInput"
                        type="file"
                        accept="application/pdf"
                        multiple
                        onChange={handleFileChange}
                        className="hidden"
                        title="Upload your Resumes (PDF)"
                    />
                    {files.length > 0 && (
                        <ul className="file-list">
                            {files.map((file, index) => (
                                <li key={index}>
                                    {file.name}
                                    <button
                                        onClick={(e) => {
                                            e.preventDefault();
                                            handleRemoveFile(index);
                                        }}
                                        className="remove-file-button"
                                    >
                                        X
                                    </button>
                                </li>
                            ))}
                        </ul>
                    )}
                </div>
                {files?.length > 0 && (
                    <div className="flex justify-center mt-5">
                        {filesChanged && files.length <= 1 && (
                            <button onClick={handleSubmit} className="primary-button">
                                Submit
                            </button>
                        )}
                    </div>
                )}
            </form>

            {response && (
                <div className="form-group">
                    <h2 style={{ color: "white", marginBottom: "5px" }}>Evaluation Result</h2>
                    <p style={{ paddingBottom: "16px" }}>
                        The score is between 0 & 100% which is calculated using the matching keyword from the job description and using our algorithm, your resume is compared to the job description to generate the score.
                    </p>
                    {renderResponse()}
                </div>
            )}
        </div>
    );
};

export default ResumeScanner;