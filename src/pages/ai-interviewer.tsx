import "../app/globals.css";

import { Images, Strings } from "@/constant";
import React, { useEffect, useRef, useState } from "react";

import { ClockCircleOutlined } from "@ant-design/icons";
import CodeEditor from "@/Component/Coding-exersice/CodeEditor";
import FinalLoading from "@/Component/finalLoading";
import Image from "next/image";
import Loading from "@/Component/Loading";
import axios from "axios";
import { useRouter } from "next/router";
import { useScreenShare } from "@/context/ScreenShareContext";

const AIInterViewer = () => {
  const router = useRouter();
  const { stream, setStream } = useScreenShare();
  const videoRef = useRef<HTMLVideoElement>(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const [time, setTime] = useState(1800);
  const minutes = Math.floor(time / 60);
  const seconds = time % 60;
  const [isDesktop, setIsDesktop] = useState(false);
  const [skills, setSkills] = useState<string[]>([]);
  const [questions, setQuestions] = useState<string[]>([]);
  const [codingQuestion, setCodingQuestions] = useState<string[]>([]);
  const [fatchQuestions, setFatchQuestions] = useState(false);
  const [audioURLs, setAudioURLs] = useState<(string | null)[]>([]);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isInterviewComplete, setIsInterviewComplete] = useState(false);
  const [generatingFeedback, setGeneratingFeedback] = useState(false);
  const [loading, setLoading] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isCodeEditorOpen, setIsCodeEditorOpen] = useState(false);
  const [submittedCode, setSubmittedCode] = useState<string>("");
  const numQuestions = 2;
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [userId, setUserId] = useState("");
  const [userData, setUserData] = useState({ name: "-", email: "-" });
  const [storedId, setStoredId] = useState("");
  let preSkills: { skill: string; level: string }[] = [];
  let isGenerateCoding = false;
  useEffect(() => {
    timerRef.current = setInterval(() => {
      setTime((prevTime) => {
        if (prevTime > 0) return prevTime - 1;
        handleCompleteInterview();
        return 0;
      });
    }, 1000);

    return () => {
      if (timerRef.current) clearInterval(timerRef.current);
    };
  }, []);

  // Commented by M36 to solve enable camera and mic issue
  useEffect(() => {
    const enableCameraAndMic = async () => {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({
          video: true,
          audio: true,
        });
        if (videoRef.current) {
          videoRef.current.srcObject = stream;
          
          // Start recording video
          const mediaRecorder = new MediaRecorder(stream, {
            mimeType: 'video/mp4;codecs=vp9'
          });
          
          const chunks: BlobPart[] = [];
          
          // Request data every second
          mediaRecorder.ondataavailable = (e) => {
            if (e.data && e.data.size > 0) {
              chunks.push(e.data);
              // Create and save blob after each chunk
              const blob = new Blob(chunks, { type: 'video/mp4' });
              const videoUrl = URL.createObjectURL(blob);
              localStorage.setItem('recordedVideo', videoUrl);
              console.log('Video chunk saved, total chunks:', chunks.length);
            }
          };
          
          mediaRecorder.onstop = () => {
            const finalBlob = new Blob(chunks, { type: 'video/mp4' });
            const finalVideoUrl = URL.createObjectURL(finalBlob);
            localStorage.setItem('recordedVideo', finalVideoUrl);
            console.log('Final video recording saved, total size:', finalBlob.size);
          };
          
          // Start recording with timeslice of 1000ms (1 second)
          mediaRecorder.start(1000);
          
          // Store mediaRecorder reference for cleanup
          const mediaRecorderRef = mediaRecorder;
          
          // Stop recording after 30 minutes or when component unmounts
          const timeoutId = setTimeout(() => {
            if (mediaRecorderRef.state === 'recording') {
              mediaRecorderRef.stop();
            }
          }, 30 * 60 * 1000);
          
          // Cleanup function
          return () => {
            clearTimeout(timeoutId);
            if (mediaRecorderRef.state === 'recording') {
              mediaRecorderRef.stop();
            }
            stream.getTracks().forEach(track => track.stop());
          };
        }
      } catch (err) {
        console.error("Error accessing camera:", err);
      }
    };

    enableCameraAndMic();
  }, []);
  
  useEffect(() => {
    const checkIsDesktop = () => {
      setIsDesktop(window.innerWidth >= 1024);
    };
    checkIsDesktop();
    window.addEventListener("resize", checkIsDesktop);
    return () => {
      window.removeEventListener("resize", checkIsDesktop);
    };
  }, []);

  const navigateToReport = () => {
    if (stream) {
      stream.getTracks().forEach((track) => track.stop());
      setStream(null);
    }
  };

  useEffect(() => {
    if (router.query.skills) {
      const parsedSkills = decodeURIComponent(
        router.query.skills as string
      ).split(",");
      setSkills(parsedSkills);
    }
  }, [router.query.skills]);

  useEffect(() => {
    const fetchData = async () => {
      if (skills.length > 0) {
        // Reset tabSwitchCount at the start of each interview
        localStorage.setItem("tabSwitchCount", "0");
        console.log("Tab switch count reset to 0 for new interview");
        
        await fetchICodingQuestions();
        // setTimeout(fetchICodingQuestions, 2000);
      }
    };

    fetchData();
  }, [skills]);

  // const fetchInterviewQuestions = async () => {
  //   setFatchQuestions(true);
  //   try {
  //     const userVettingData = await axios.get(
  //       `${process.env.NEXT_PUBLIC_API_BASE_URL}gptVetting/get?id=${localStorage.getItem("userId")}`
  //     );

  //     const predefinedSkills =
  //       userVettingData.data?.gptVettingData?.techStack || [];

  //     if (!predefinedSkills.length) {
  //       throw new Error("No predefined skills found.");
  //     }

  //     const techStack: string[] = userVettingData.data?.gptVettingData?.techStack || [];
  //     const techLevel: string[] = userVettingData.data?.gptVettingData?.techLevel || [];

  //     if (!techStack.length || !techLevel.length) {
  //       throw new Error("Missing techStack or techLevel");
  //     }

  //     preSkills = techStack.map((skill: string, index: number) => ({
  //       skill,
  //       level: techLevel[index]?.toLowerCase() || "junior",
  //     }));

  //     isGenerateCoding = userVettingData.data?.gptVettingData?.isCodingExcercise || false;

  //     // const data = JSON.stringify(skills);
  //     const data = JSON.stringify(preSkills);
  //     console.log("Request Payload:", data);

  //     const config = {
  //       method: "post",
  //       maxBodyLength: Infinity,
  //       url: `${process.env.NEXT_PUBLIC_API_BASE_URL}gptVetting/generateQuestions?num_questions=${numQuestions}`,
  //       // url: `${process.env.NEXT_PUBLIC_GPT_VETTING_API_URL}interviews/generate-questions?num_questions=${numQuestions}`,
  //       headers: {
  //         accept: "application/json",
  //         "Content-Type": "application/json",
  //         Cookie:
  //           ".Tunnels.Relay.WebForwarding.Cookies=CfDJ8Cs4yarcs6pKkdu0hlKHsZv-4Jzg7WxLamtRC5Ty88OQN1SVRKHqPnhdYyXvBIARLQlLQtOzi8yiPY4AHk8XJG-7JP_3QYZVk0GQ7jOEGIl8KKLGgwYztv-FkZwGuJDEQ33bVvSuFi_drGCdXr8NLaaIAcI81OPPpNZRWeJJk78e7I6NhOUZ1CS89Eg5YNEHei_xEnaH57WVCInB8dLTuW7Z-ClrJ7R4WXW_Dva6JlH_ze0YH1ykVRR3d0dWZQge3LHbJrrpupsJZsqnfYT3u2YyQFY-ttRYknVHxgZLvklt00qqyPslNVSEkZQEGmq2ts7XE7bDiOsLFd82q0nADqj_hJOiPYBa6_kmqi70lyFI4hP2NYfw8WkVpuKNguNgEZe91NYOAcFbDR7FUvOPx0a1-E4WXNuyWQCdxF2NeoXyv8gEJdM0MOhCQajrzwbVFJG7HzzcY77H6onOhzL5zVMrNSBrB6x4NaE_Ypk8OKFNP-Zoj9oXbgKhb0PqIkvH-h995kg01xf05ZwaX0_gphOPQb-wZZ1SEiMzJ--IzRDPr6hMx0-CUzKRxySpZRb77stYsXvYfhCO8c4jbXRzNuEIbVuBSJdJ0J0stv7xUU6Rc1sqt7DoTPI6j8Z0s3bvyKR8Uuh2QsmB3SLFvWG9a_eyl-e8XihSI35QK9v_ZV_4LyL0ZMhSFFMHdNMIKuk1OzSwXPx8T1uVxliNunpVaTeXxfoenZui8LV_6BKkDeDe_3nM02TU-ZmDhAKLXYHZzOqn2l6ftInerzfPsl57Zq2ce3NGw243CcsAEosjQjaxXgtDfSifrQBCHld1RyGHcAoNtAOQaKmE7F2ZiYK_c4HJzrrGutK6aDGZrOahaZU8ppeKhglrgizN30N9of-aNJzyQMU_Docxnsu4c2nOwB9Umor9wPOcrQN8DjsP6nX0",
  //       },
  //       data: data,
  //     };

  //     console.log("API Request Config:", config);

  //     const response = await axios.request(config);
  //     console.log("API Response:", response.data);

  //     setQuestions(response.data.questions);
  //   } catch (error) {
  //     console.error("Error fetching interview questions:", error);
  //   } finally {
  //     setFatchQuestions(false);
  //   }
  // };


  const fetchICodingQuestions = async () => {
    try {
      const userVettingData = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}gptVetting/get?id=${localStorage.getItem("userId")}`
      );

      const predefinedSkills =
        userVettingData.data?.gptVettingData?.techStack || [];

      if (!predefinedSkills.length) {
        throw new Error("No predefined skills found.");
      }

      const techStack: string[] = userVettingData.data?.gptVettingData?.techStack || [];
      const techLevel: string[] = userVettingData.data?.gptVettingData?.techLevel || [];

      if (!techStack.length || !techLevel.length) {
        throw new Error("Missing techStack or techLevel");
      }

      preSkills = techStack.map((skill: string, index: number) => ({
        skill,
        level: techLevel[index]?.toLowerCase() || "junior",
      }));

      isGenerateCoding = userVettingData.data?.gptVettingData?.isCodingExcercise || false;

      const data = JSON.stringify(preSkills);
      let url = "";
      if (isGenerateCoding) {
        url = `${process.env.NEXT_PUBLIC_API_BASE_URL}gptVetting/generateQuestions?num_questions=${numQuestions}&is_coding_excercise=${isGenerateCoding}`;
      } else {
        url = `${process.env.NEXT_PUBLIC_API_BASE_URL}gptVetting/generateQuestions?num_questions=${numQuestions}`;
      }
      const config = {
        method: "post",
        maxBodyLength: Infinity,
        url: url,
        headers: {
          accept: "application/json",
          "Content-Type": "application/json",
          Cookie:
            ".Tunnels.Relay.WebForwarding.Cookies=CfDJ8Cs4yarcs6pKkdu0hlKHsZtgL9IGB-oyRJp_lM5z7QMHDc9P3fJsGiT1DnbHN00R3piHPCHcE6K8aLm6Jx_UdChi7Ao6lxXIvxjhVazn_x3c_mbTRTbOTGVc0tnd25rCto-TaEqK9h-UiUEK8qKrGpgaMJ4wewBX5xkvvWlB1Miw7Q9dni0dDyOevIF7tBGxT-JEwK0kyPb6KpeyFN8cgHBz53rkaOUIVEritufupXkX0RqwIURqwcDOoarXMvkHoQe85zNFg9EZl1PnHl-FSijTsC3anEJ28aFYLFRm7ayr3u4JLb1xMchF6VcT08Jgrh8gaI1jvUjSmOaVLYgHLEvFJYvhdig9oSJke7K9yDCalGEjbuCIlY2GaeU-W_E9fAn6QNLCIZZubC5M3ytTGraiTrTxCh3su03YGLekyY1-uibFjqVGd0VVoi9Hoqmirg_tE-QqjLSpg-GtQw9WiK5Gs0jO8EmpkxyZWlCbynBlFOD5PmycBRL1FTyv-enPqmEPl5XtDB7hcu-NROEDVJFGPLx3eJhXw1eLO7KCMAQO5Js3IvKnluloXTD_BFujBpb0rrppSHGidoi29lwbY1HWqzeQPNOofmOPkOHuWGNo1EhSamJlWtXGUtZxfaUQMhpxYV4e4GGO9s55uDmJZq1LUeRIcw5dohD5USlVqfZxR21gq_419l_qLqtWA3y9gcq6F3210CX1WASxxxb_JqwI3ewQADG2EjuHPK0J-kgRTV417c8jjOZr6WZ3_IAhab91DtZpkBXQAoGgWKb1nc-s8cwSkliU3k8Fys6hInRookoDCgIvc7Z5O1V3uvCybfxwMJAMl73NnSo1mgXIHOwasGDjwS814XbcfMghKi600dZLe6hKoiUAZo0p4Z-hkPClpvU0abfCYN-l1MrDXY5CY3KjGQaVGsqOmnbvX6PT",
        },
        data: data,
      };
      const response = await axios.request(config);
      setQuestions(response.data.questions);
      setCodingQuestions(response.data.codingQuestions);
    } catch (error) {
      console.error("Error fetching coding questions:", error);
    }
  };

  useEffect(() => {
    const storedId = localStorage.getItem("userId");
    if (storedId) {
      fetchUserDetails(storedId);
    }
  }, []);

  const fetchUserDetails = async (id: string) => {
    try {
      const response = await axios.get(`${process.env.NEXT_PUBLIC_API_BASE_URL}gptVetting/get?id=${id}`);
      if (response.data && response.data.gptVettingData) {
        const { name, email } = response.data.gptVettingData;
        setUserData({
          name: name || "-",
          email: email || "-"
        });
        if (response.data.gptVettingData.userId) {
          setUserId(response.data.gptVettingData.userId);
        }
      }
    } catch (error) {
      console.error("Error fetching user details:", error);
    }
  };

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      audioChunksRef.current = [];
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };
      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, {
          type: "audio/mp3",
        });
        const audioUrl = URL.createObjectURL(audioBlob);
        setAudioURLs((prev) => {
          const updatedAudioURLs = [...prev, audioUrl];
          return updatedAudioURLs;
        });
      };
      mediaRecorder.start();
      mediaRecorderRef.current = mediaRecorder;
    } catch (error) {
      // console.error("Error accessing microphone:", error);
    }
  };



  useEffect(() => {
    if (videoRef.current) {
      console.log("✅ videoRef found!", videoRef.current);
      if (videoRef.current.srcObject) {
        console.log("🎥 Video is already set.");
      } else {
        console.warn("❌ No video feed yet!");
      }
    }
  }, [videoRef.current]);

  const stopRecording = () => {
    if (mediaRecorderRef.current) {
      mediaRecorderRef.current.stop();
    }
  };

  const handleNextQuestion = async () => {
    stopRecording();
    if (currentQuestionIndex < questions.length - 1) {
      setIsGenerating(true);
      setTimeout(() => {
        setCurrentQuestionIndex((prevIndex) => prevIndex + 1);
        setIsGenerating(false);
        startRecording();
      }, 1500);
    } else {
      if (codingQuestion.length > 0) {
        setIsCodeEditorOpen(true);
      } else {
        setIsCodeEditorOpen(false);
      } 
      setIsInterviewComplete(true);
    }
  };

  const handleCodeSubmit = (code: string) => {
    setSubmittedCode(code);
  };

  const stopTimer = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
  };

  const handleCompleteInterview = async () => {
    setIsInterviewComplete(true);
    stopTimer();
    
    // Stop video recording if stream exists
    if (videoRef.current && videoRef.current.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream;
      stream.getTracks().forEach(track => {
        if (track.kind === 'video' && track.readyState === 'live') {
          track.stop();
        }
      });
    }
    
    navigateToReport();
    new Promise((resolve) => setTimeout(resolve, 1500)).then(() => {
      handleSubmit();
    });
  };

  const speakText = async (text: string) => {
    if ('speechSynthesis' in window) {
      stopRecording(); // By M36
      setIsSpeaking(true);
      
      // Cancel any ongoing speech
      window.speechSynthesis.cancel();

      // Split text into sentences (chunks)
      const sentences = text.match(/[^.!?]+[.!?]+/g) || [text];
      let currentIndex = 0;

      const speakNextChunk = () => {
        if (currentIndex < sentences.length) {
          const utterance = new SpeechSynthesisUtterance(sentences[currentIndex]);
          
          // Get available voices
          let voices = window.speechSynthesis.getVoices();
          const englishVoice = voices.find(voice => 
            voice.lang.startsWith('en-') && voice.name.includes('Female')
          ) || voices.find(voice => 
            voice.lang.startsWith('en-')
          ) || voices[0];

          utterance.voice = englishVoice;
          utterance.rate = 1;
          utterance.pitch = 1;
          utterance.volume = 1;

          utterance.onend = () => {
            currentIndex++;
            speakNextChunk();
          };

          utterance.onerror = () => {
            console.error('Text-to-speech error');
            setIsSpeaking(false);
          };

          window.speechSynthesis.speak(utterance);
        } else {
          setIsSpeaking(false);
          startRecording(); // By M36
        }
      };

      // Start speaking from the first chunk
      speakNextChunk();

      // Add a periodic check to ensure speech synthesis hasn't stopped unexpectedly
      const keepAlive = setInterval(() => {
        if (!window.speechSynthesis.speaking) {
          clearInterval(keepAlive);
        } else {
          window.speechSynthesis.pause();
          window.speechSynthesis.resume();
        }
      }, 5000);
    } else {
      console.warn('Text-to-speech not supported');
    }
  };

  // Add tab switch tracking
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        const currentCount = parseInt(localStorage.getItem("tabSwitchCount") || "0");
        localStorage.setItem("tabSwitchCount", (currentCount + 1).toString());
        console.log("Tab switch detected, new count:", currentCount + 1);
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);
    
    // Initialize tabSwitchCount if not exists
    if (!localStorage.getItem("tabSwitchCount")) {
      localStorage.setItem("tabSwitchCount", "0");
    }

    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, []);

  const handleSubmit = async () => {
    setLoading(true);
    const videoURL = localStorage.getItem("recordedVideo");
    const storedId = localStorage.getItem("userId");
    
    try {
      // First upload all files to Google Cloud Storage
      const uploadedFiles = {
        videoUrl: '',
        audioUrls: [] as string[]
      };

      // Upload video if exists - using previous working code
      if (videoURL) {
        console.log("🎥 Starting video upload process...");
        console.log("Video URL from localStorage:", videoURL.substring(0, 100) + "...");
        
        try {
          console.log("1. Fetching video blob...");
          const videoBlob = await fetch(videoURL).then((res) => res.blob());
          console.log("Video blob size:", videoBlob.size, "bytes");
          console.log("Video blob type:", videoBlob.type);

          console.log("2. Creating video file...");
          const videoFile = new File([videoBlob], "interview_video.mp4", {
            type: "video/mp4",
          });
          console.log("Video file created:", {
            name: videoFile.name,
            size: videoFile.size,
            type: videoFile.type
          });
          
          // Get signed URL for video
          console.log("3. Getting signed URL for video...");
          const videoSignedUrlResponse = await axios.get(
            `${process.env.NEXT_PUBLIC_API_BASE_URL}gptVetting/signed-url?id=${storedId}&filename=interview_video.mp4&contentType=video/mp4`
          );
          
          console.log("4. Signed URL response:", {
            status: videoSignedUrlResponse.status,
            hasSignedUrl: !!videoSignedUrlResponse.data?.signedUrl
          });
          
          if (!videoSignedUrlResponse.data?.signedUrl) {
            throw new Error("No signed URL received for video");
          }

          // Upload video to GCS
          console.log("5. Starting upload to signed URL...");
          await axios.put(videoSignedUrlResponse.data.signedUrl, videoFile, {
            headers: { 'Content-Type': "video/mp4" },
            onUploadProgress: (progressEvent) => {
              const percentCompleted = Math.round((progressEvent.loaded * 100) / (progressEvent.total || progressEvent.loaded));
              console.log(`Video upload progress: ${percentCompleted}%`);
            }
          });

          // Store the public URL (without query parameters)
          uploadedFiles.videoUrl = videoSignedUrlResponse.data.signedUrl.split('?')[0];
          console.log("6. Video upload completed successfully!");
          console.log("Final video URL:", uploadedFiles.videoUrl);
        } catch (error) {
          console.error("❌ Error in video upload process:", error);
          throw error; // Re-throw to be caught by outer try-catch
        }
      } else {
        console.log("⚠️ No video URL found in localStorage");
      }

      // Upload all audio files (keeping current working code)
      console.log(`🎤 Uploading ${audioURLs.length} audio files...`);
      for (let i = 0; i < audioURLs.length; i++) {
        const audioURL = audioURLs[i];
        if (!audioURL) continue;

        console.log(`Processing audio file ${i + 1}/${audioURLs.length}`);
        const audioBlob = await fetch(audioURL).then(res => res.blob());
        const audioFile = new File([audioBlob], `interview_audio_${i + 1}.mp3`, {
          type: "audio/mp3"
        });

        // Get signed URL for audio
        console.log(`Getting signed URL for audio ${i + 1}...`);
        const audioSignedUrlResponse = await axios.get(
          `${process.env.NEXT_PUBLIC_API_BASE_URL}gptVetting/signed-url?id=${storedId}&filename=interview_audio_${i + 1}.mp3&contentType=audio/mp3`
        );

        if (!audioSignedUrlResponse.data?.signedUrl) {
          throw new Error(`No signed URL received for audio ${i + 1}`);
        }

        // Upload audio to GCS
        console.log(`Uploading audio ${i + 1} to signed URL...`);
        await axios.put(audioSignedUrlResponse.data.signedUrl, audioFile, {
          headers: { 'Content-Type': "audio/mp3" },
          onUploadProgress: (progressEvent) => {
            const percentCompleted = Math.round((progressEvent.loaded * 100) / (progressEvent.total || progressEvent.loaded));
            console.log(`Audio ${i + 1} upload progress: ${percentCompleted}%`);
          }
        });

        // Store the public URL (without query parameters)
        uploadedFiles.audioUrls.push(audioSignedUrlResponse.data.signedUrl.split('?')[0]);
        console.log(`Audio ${i + 1} uploaded successfully:`, uploadedFiles.audioUrls[i]);
      }

      // Now create FormData with URLs instead of files
      let data = new FormData();
      // Use static userId
      data.append("userId", "RH0007");
      questions.forEach((question) => {
        data.append("question[]", question);
      });
      
      uploadedFiles.audioUrls.forEach((audioUrl) => {
        data.append("answer[]", audioUrl);
      });

      if (uploadedFiles.videoUrl) {
        data.append("video", uploadedFiles.videoUrl);
      }

      data.append("task", JSON.stringify(codingQuestion.join("\n\n")));
      
      // Get the current tab switch count
      const tabSwitchCount = localStorage.getItem("tabSwitchCount") || "0";
      console.log("Final tab switch count:", tabSwitchCount);
      data.append("tabSwitchCount", tabSwitchCount);

      data.append("name", userData.name);
      data.append("email", userData.email);
      
      // Ensure code is passed
      if (submittedCode) {
        data.append("code", submittedCode);
      }

      const techStack = localStorage.getItem("skills")
      ? JSON.parse(localStorage.getItem("skills") || "[]")
      : [];

      data.append("techStack", JSON.stringify(techStack));
      data.append("testTaken", "Self defined skills");

      let config = {
        method: "post",
        maxBodyLength: Infinity,
        maxContentLength: Infinity,
        timeout: 1800000,
        url: `${process.env.NEXT_PUBLIC_API_BASE_URL}gptVetting/update?id=${storedId}`,
        headers: {},
        data: data,
      };
      
      // const response = await axios.request(config);
      await axios.request(config)
      .then((response) => {
        console.log(JSON.stringify(response.data));
        // Show success popup and redirect
        setGeneratingFeedback(true);
        
        // Clear localStorage
        localStorage.removeItem("candidate_email");
        localStorage.removeItem("candidate_name");
        localStorage.removeItem("skills");
        localStorage.removeItem("tabSwitchCount");
        localStorage.removeItem("recordedVideo");
        
        // setTimeout(() => {
        //   window.location.href = "/";
        // }, 2000);

        localStorage.removeItem("recordedVideo");
      })
      .catch((error) => {
        console.log(error);
      });

      // if (response) {
        // const aiResponse = await callAiSubmitInterview(
        //   questions,
        //   uploadedFiles.audioUrls,
        //   uploadedFiles.videoUrl,
        //   codingQuestion,
        //   submittedCode,
        //   "RH0007" // Use static userId here
        // );

        // if (!aiResponse || !aiResponse.data) {
        //   throw new Error("Failed to get AI feedback");
        // }

        // const techStack = localStorage.getItem("skills")
        //   ? JSON.parse(localStorage.getItem("skills") || "")
        //   : [];

        // await axios.post(
        //   `${process.env.NEXT_PUBLIC_API_BASE_URL}gptVetting/update?id=${storedId}`,
        //   {
        //     skillRating: aiResponse.data?.skill_rating,
        //     proctoringResult: aiResponse.data?.proctoring_score,
        //     codeFeedback: aiResponse.data?.code_feedback,
        //     feedback: aiResponse.data?.feedback,
        //     noFaceDetectedCount: aiResponse.data?.no_face_detected_count,
        //     lookingAwayCount: aiResponse.data?.looking_away_count,
        //     techStack,
        //     testTaken: "Self defined skills",
        //   }
        // );
        
        // Show success popup and redirect
      //   setGeneratingFeedback(true);
        
      //   // Clear localStorage
      //   localStorage.removeItem("candidate_email");
      //   localStorage.removeItem("candidate_name");
      //   localStorage.removeItem("skills");
      //   localStorage.removeItem("tabSwitchCount");
      //   localStorage.removeItem("recordedVideo");
        
      //   setTimeout(() => {
      //     router.push('/');
      //   }, 2000);
      // }
    } catch (error) {
      console.error("Error in interview submission:", error);
      // Show error popup instead of success
      setGeneratingFeedback(false);
      
      // Handle error message extraction
      let errorMessage = "Unknown error";
      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === 'object' && error !== null && 'message' in error) {
        errorMessage = String(error.message);
      } else if (error !== null && error !== undefined) {
        errorMessage = String(error);
      }
      
      alert("Sorry, we couldn't process your interview. Please try again. Error: " + errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const callAiSubmitInterview = async (
    questions: string[],
    audioUrls: string[],
    videoUrl: string,
    codingQuestion: string[],
    submittedCode: string,
    userId: string
  ) => {
    try {
      let data = new FormData();
      // Use static userId
      data.append("user_id", "RH0007");
      
      questions.forEach((question) => {
        data.append("questions", question);
      });

      // Add audio URLs array for submit API
      audioUrls.forEach((audioUrl) => {
        data.append("audio_urls", audioUrl);
      });

      // Add video URL for submit API
      if (videoUrl) {
        data.append("video_url", videoUrl);
      }

      data.append("code_question", JSON.stringify(codingQuestion.join("\n\n")));
      
      // Ensure code is passed
      if (submittedCode) {
        data.append("code", submittedCode);
      }

      const tabSwitchCount = localStorage.getItem("tabSwitchCount") || "0";
      data.append("tabSwitchCount", tabSwitchCount);

      let config = {
        method: "post",
        maxBodyLength: Infinity,
        maxContentLength: Infinity,
        url: `${process.env.NEXT_PUBLIC_GPT_VETTING_API_URL}interviews/submit`,
        headers: { "Content-Type": "multipart/form-data" },
        timeout: 30 * 60 * 1000,
        data: data,
      };
      const response = await axios.request(config);
      return response;
    } catch (error) {
      console.log(error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (questions.length > 0) {
      startRecording();
    }
  }, [questions]);

  const GenerateFeedback = async () => {
    setIsLoading(true);
    const videoURL = localStorage.getItem("recordedVideo");
    try {
      let data = new FormData();
      data.append("user_id", "RH00007");
      questions.forEach((question) => {
        data.append("questions", question);
      });
      await Promise.all(
        audioURLs.map(async (audio: any, index) => {
          try {
            const response = await fetch(audio);
            if (!response.ok)
              throw new Error(`Failed to fetch audio: ${audio}`);
            const audioBlob = await response.blob();
            const audioFile = new File([audioBlob], `audio_${index}.mp3`, {
              type: "audio/mp3",
            });
            data.append("audio_files", audioFile);
          } catch (error) {
            console.error(`Error processing audio file ${index}:`, error);
          }
        })
      );
      if (videoURL) {
        const videoBlob = await fetch(videoURL).then((res) => res.blob());
        const videoFile = new File([videoBlob], "video.mp4", {
          type: "video/mp4",
        });
        data.append("video_file", videoFile);
      }
      codingQuestion.forEach((codingQus) => {
        data.append("code_question", codingQus);
      });
      data.append("code", submittedCode);
      let config = {
        method: "post",
        maxBodyLength: Infinity,
        maxContentLength: Infinity,
        // url: `${process.env.NEXT_PUBLIC_GPT_VETTING_API_URL}interviews/submit`,
        url: `${process.env.NEXT_PUBLIC_API_BASE_URL}gptVetting/submit`,
        headers: { "Content-Type": "multipart/form-data" },
        data: data,
      };
      const response = await axios.request(config);
      console.log("Feedback =>", JSON.stringify(response.data));
      router.push({
        pathname: "/ai-interview-report",
        query: {
          skill_rating: response.data.skill_rating,
          proctoring_score: response.data.proctoring_score,
          feedback: JSON.stringify(response.data.feedback),
        },
      });
    } catch (error) {
      console.log(error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (questions.length > 0 && currentQuestionIndex < questions.length) {
      const questionText = questions[currentQuestionIndex].replace(/\*\*(.*?)\*\*/g, "$1");
      speakText(questionText);
    }
  }, [currentQuestionIndex, questions]);

  return (
    <>
      <div className="hidden max-lg:block fixed bg-white z-20 flex justify-center items-center h-full w-full">
        <h1 className="font-normal text-black text-sm mx-5 text-center">
          <span className="bg-gradient-to-l font-medium from-contessa to-tosca text-transparent bg-clip-text">
            {Strings.eRemoteHire}
          </span>{" "}
          Ai-interviewer is only accessible on desktop devices and with
          developer tools closed.
        </h1>
      </div>
      <div className="xl:bg-[linear-gradient(to_top,_#241415,_black)] h-screen w-screen">
        <div className="relative bg-[linear-gradient(to_top,_#241415,_black)] h-screen w-screen max-w-screen-2xl m-auto flex justify-center items-center">
          <div className="fixed top-0 items-center w-full flex justify-between p-5 lg:p-7">
            <Image
              src={Images.logo}
              alt="/"
              width={500}
              height={500}
              className="w-[165px] h-[35px] -ml-4"
            />
            <div className="bg-gradient-to-l from-[#481e1f] to-[#6b443a] rounded-md w-[100px] p-[1px] z-20">
              <div className=" flex items-center justify-center gap-x-2 rounded-md bg-[linear-gradient(to_top,_#241415,_black)] p-2 text-xl font-bold text-white">
                <ClockCircleOutlined className=" text-[#8d3f42]" />
                <p>{`${minutes}:${seconds < 10 ? "0" : ""}${seconds}`}</p>
              </div>
            </div>
          </div>
          <div className="relative h-24 w-24">
            {/* Spinning outer container */}
            <div className="absolute inset-0 p-3 rounded-full bg-transparent border border-tosca border-opacity-50 flex justify-center items-center animate-slow-spin">
              <div className="absolute inset-0 rounded-full bg-white opacity-10 blur-md animate-pulse"></div>
              <div className="absolute inset-[-2px] rounded-full border-2 border-white opacity-20 animate-pulse-delay"></div>
              
              {isSpeaking && (
                <>
                  <div className="absolute inset-[-8px] rounded-full animate-breathing">
                    <div className="absolute inset-0 rounded-full bg-white opacity-30 blur-md"></div>
                    <div className="absolute inset-0 rounded-full border-1 border-white opacity-40"></div>
                  </div>
                </>
              )}
              
              <div className="h-full w-full rounded-full bg-transparent border border-tosca border-opacity-50 flex justify-center items-center relative">
                <div className="absolute inset-0 rounded-full bg-white opacity-5 blur-sm"></div>
              </div>
            </div>

            {/* Static image container */}
            <div className="absolute inset-0 flex items-center justify-center">
              <Image
                src={"/images/favicon.png"}
                alt="/"
                width={700}
                height={700}
                className="w-12 h-12 relative z-10"
              />
            </div>
          </div>

          

          <div className="w-[320px] absolute bottom-[35%] lg:right-[10%] xl:right-[15%]">
            {isCodeEditorOpen ? (
              <div className="fixed inset-0 bg-black bg-opacity-90 flex z-10">
                <div className=" w-full h-full">
                  <CodeEditor
                    question={
                      codingQuestion.length > 0
                        ? codingQuestion.join("\n\n")
                        : "No question available."
                    }
                    minutes={minutes}
                    seconds={seconds}
                    handleContinue={() => setIsCodeEditorOpen(false)}
                    videoRef={videoRef}
                    onCodeSubmit={handleCodeSubmit}
                  />
                </div>
              </div>
            ) : !isInterviewComplete ? (
              <>
                <h1
                  className="font-normal text-white text-base text-center lg:text-start"
                  dangerouslySetInnerHTML={{
                    __html: fatchQuestions
                      ? "Fetching questions..."
                      : isGenerating
                        ? "Generating new question..."
                        : questions.length > 0 && questions[currentQuestionIndex]
                          ? questions[currentQuestionIndex].replace(
                            /\*\*(.*?)\*\*/g,
                            "<strong>$1</strong>"
                          )
                          : "We are fetching your questions.",
                  }}
                ></h1>

{!isGenerating && (
                  <div className="my-4 flex justify-center items-center gap-x-4 text-center w-full bg-black border border-[#241415] px-4 py-2 rounded-md">
                    <div className="relative flex justify-center items-center">
                      <div className="absolute bg-red-500 h-7 w-7 rounded-full bg-opacity-50 text-white animate-recording"></div>
                      <div className="bg-red-500 h-4 w-4 rounded-full"></div>
                    </div>
                    {/* {isSpeaking ? "Speaking..." : "Recording..."} */}
                    {
                      isSpeaking
                        ? "Speaking..."
                        : isGenerating
                        ? "Generating question..."
                        : isGenerating || fatchQuestions
                        ? "Please wait..."
                        : "Recording..."
                    }
                  </div>
                )}

                {!isGenerating && !isSpeaking && (
                  <button
                    onClick={handleNextQuestion}
                    disabled={isGenerating || fatchQuestions || isSpeaking}
                    className={`w-full bg-gradient-to-r from-[#481e1f] to-[#6b443a] text-white transition-transform hover:scale-105 transform duration-700 px-4 py-2 rounded-full ${
                      isGenerating || fatchQuestions || isSpeaking
                        ? "opacity-50 cursor-not-allowed"
                        : ""
                    }`}
                  >
                    Done answering? Continue
                  </button>
                )}
              </>

            ) : (
              <>
                <h1 className="font-normal text-white text-base text-center lg:text-start">
                Please submit your Interview. This may take a few minutes. Please wait while we process your submission.
                </h1>
                <button
                  onClick={handleCompleteInterview}
                  className="my-4 w-full bg-gradient-to-r from-[#481e1f] to-[#6b443a] transition-transform hover:scale-105 transform duration-700 text-white flex justify-center items-center h-10 rounded-full"
                >
                  {loading ? <FinalLoading /> : "Submit"}
                </button>
              </>
            )}
          </div>
          {/* {generatingFeedback && (
            <div className="z-10 fixed bg-white flex justify-center items-center h-full w-full">
              <div className="text-center space-y-6">
                <Image
                  src={Images.Logo}
                  alt="/"
                  width={1200}
                  height={1200}
                  className="w-40 h-9 justify-self-center flex"
                />
                <h1 className="text-black font-semibold text-xl">
                  Congratulations! Your AI interview has been completed
                  successfully.
                  <br />
                </h1>
                <button
                  onClick={GenerateFeedback}
                  className="flex justify-center text-center justify-self-center items-center w-[183px] h-12 text-white font-medium text-base bg-gradient-to-r from-[#481e1f] to-[#6b443a] transition-transform hover:scale-105 transform duration-300 rounded-full"
                >
                  {isLoading ? <Loading /> : "Generate Report"}
                </button>
              </div>
            </div>
          )} */}
          <div className="absolute bottom-20 left-20 h-48 w-[320px] rounded-md bg-gradient-to-r from-[#481e1f] to-[#6b443a] p-[2px]">
            <div className="h-full w-full rounded-md bg-black">
              <video
                ref={videoRef}
                autoPlay
                playsInline
                muted
                className="w-full h-full rounded-md object-cover"
                style={{ transform: 'scaleX(-1)'}}
              />
            </div>
          </div>
          <div className="fixed bottom-0 w-full bg-gradient-to-l from-[#481e1f] to-[#6b443a] pt-[2px]">
            <div className="relative">
              <button
                onClick={handleCompleteInterview}
                className={`bg-gradient-to-r inline-block from-[#481e1f] to-[#6b443a] text-white transition-transform hover:scale-105 transform duration-700 px-4 py-2 rounded-full  absolute right-4 bottom-5`}
              >
                End Interview
              </button>
            </div>
            <div className="bg-[linear-gradient(to_bottom,_#241415,_black)] items-center font-normal text-white text-sm w-full flex justify-between p-2 lg:p-4">
              <h1>{Strings.Note} </h1>
              <h1>
                Powered by{" "}
                <span className="bg-gradient-to-l font-medium from-contessa to-tosca text-transparent bg-clip-text">
                  eRemoteHire
                </span>
              </h1>
            </div>
          </div>
        </div>
      </div>

      {/* Success/Error Popup */}
      {generatingFeedback && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
          <div className="bg-white rounded-lg p-8 max-w-md w-full mx-4 text-center">
            <div className="mb-6">
              <svg className="w-16 h-16 text-green-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
              </svg>
              <h2 className="text-2xl font-semibold text-gray-900 mb-2">
                Interview Submitted Successfully!
              </h2>
              <p className="text-gray-600">
                Thank you for completing your interview.
              </p>
            </div>
            <div className="animate-pulse">
              <div className="h-1 w-full bg-gradient-to-r from-[#481e1f] to-[#6b443a] rounded"></div>
            </div>
            <button
              onClick={() =>  window.location.href = "/gpt-vetting"}
              className="bg-gradient-to-r from-[#481e1f] to-[#6b443a] text-white transition-transform hover:scale-105 transform duration-700 px-4 py-2 rounded-full mt-6"
            >
              OK
            </button>
          </div>
        </div>
      )}
    </>
  );
};

export default AIInterViewer;