@import url("https://fonts.googleapis.com/css2?family=Outfit:wght@400;500;600;700&display=swap");

* {
  margin: 0;
  padding: 0;
  font-style: normal;
  font-family: "Outfit", sans-serif;
  scroll-behavior: smooth;
}

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
    --animation-duration: 1s;
    --animation-delay: 1s;
    --animation-repeat: 1s;
  }
}

body {
  color: rgb(var(--foreground-rgb));
  background-color: black;
  background: black;
  overflow-x: hidden;
  font-style: normal;
  scroll-behavior: smooth;
}

.custom-bg {
  background-image: linear-gradient(rgba(141, 63, 66, 1),
      rgba(188, 118, 102, 1));
  color: #fff;
  text-align: center;
  font-family: Outfit;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  border-radius: 30px;
  border: none;
  outline: none;
}

.custom-bg:hover {
  background-image: linear-gradient(rgb(0, 0, 0), rgb(0, 0, 0));
  color: #fff !important;
}

.login:hover {
  background-image: linear-gradient(rgba(141, 63, 66, 1),
      rgba(188, 118, 102, 1));
  color: #fff !important;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  border-radius: 30px;
  border: none;
  outline: none;
}

.custom-text {
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  color: rgb(var(--background-end-rgb)) rgb(var(--background-start-rgb));
  overflow-x: hidden;
}

.text {
  font-weight: bold;
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  background-image: linear-gradient(to right,
      rgba(141, 63, 66, 1),
      rgba(188, 118, 102, 1));
}

.active-image {
  border: 3px solid #bc7666;
  border-radius: 10px;
}

.hover-image {
  border: 5px solid #bc7666;
  border-radius: 10px;
  width: 100px;
  height: 100px;
}

.atom-nucleus {
  background-color: #38b6ff;
  border-radius: 50%;
  left: 50%;
  position: absolute;
  top: 50%;
}

.electron {
  background-color: white;
  border-radius: 50px;
  left: 50%;
  position: absolute;
  top: 0%;
  rotate: 0deg !important;
}

.planet-cyan {
  background-color: cyan !important;
}

.nice-shadow {
  box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
}

.atom-orbit {
  border-radius: 50%;
  position: absolute;
}

.atom_orbit_photoshop {
  rotate: 265deg !important;
}

.atom_orbit_typescript {
  rotate: 180deg !important;
}

.atom_orbit_aws {
  rotate: 88deg !important;
}

@keyframes spin-right {
  100% {
    -webkit-transform: rotate(360deg);
  }
}

@keyframes spin-left {
  100% {
    -webkit-transform: rotate(-360deg);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 15s linear infinite;
}

@keyframes pulse {
  0% {
    -webkit-transform: scaleX(1);
    transform: scaleX(1);
  }

  50% {
    -webkit-transform: scale3d(1.05, 1.05, 1.05);
    transform: scale3d(1.05, 1.05, 1.05);
  }

  to {
    -webkit-transform: scaleX(1);
    transform: scaleX(1);
  }
}

.pulse {
  -webkit-animation-name: pulse;
  animation-name: pulse;
  -webkit-animation-timing-function: ease-in-out;
  animation-timing-function: ease-in-out;
}

.more-info {
  opacity: 0;
  transition: 0.5s ease;
}

.active {
  opacity: 1;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 5s linear infinite;
}

@import "~slick-carousel/slick/slick.css";
@import "~slick-carousel/slick/slick-theme.css";

.App {
  width: 100%;
  margin: 10rem auto;
  height: 570px;
}

.slide img {
  width: 35rem;
  align-items: center;
  margin: 0 auto;
  z-index: 1;
}

.slide {
  transform: scale(0.8);
  transition: transform 300ms;
  opacity: 0.5;
  z-index: -1;
}

.activeSlide {
  transform: scale(1.1);
  align-items: center;
  opacity: 1;
}

.menu {
  width: 100%;
  height: 100%;
  background-color: black;
  transition: transform 2s ease;
}

.menu.open {
  transform: translateY(-100%);
  /* Slide down to show */
}

.menu.closed {
  transform: translateY(-100%);
  /* Slide up to hide */
}

/* How it work css */

ul.cards-split {
  width: 660px;
  height: 280px;
  list-style-type: none;
  position: relative;
  margin: 0 auto 20px;
  /* padding: 20px; */
  cursor: pointer;
  background-color: black;
}

ul.cards-split li.title {
  margin: 0 0 20px;
}

ul.cards-split li.title h2 {
  font-weight: 700;
}

ul.cards-split li.card {
  /* background: black; */

  overflow: hidden;
  height: 300px;
  width: 250px;
  border-radius: 10px;
  position: absolute;
  left: 220px;
  transition: all 2s cubic-bezier(0.63, 0.15, 0.03, 1.12);
}

ul.cards-split li.card img {
  max-width: 100%;
  height: auto;
}

ul.cards-split li.card div.content {
  padding: 5px 10px;
}

ul.cards-split li.card.card-1 {
  z-index: 10;
  transform: rotateZ(-7deg);
}

ul.cards-split li.card.card-2 {
  z-index: 9;
  transform: rotateZ(7deg);
  transition-delay: 0.05s;
}

ul.cards-split li.card.card-3 {
  z-index: 8;
  transform: rotateZ(-18deg);
  transition-delay: 0.1s;
}

ul.cards-split.transition li.card {
  transform: rotateZ(0deg);
  background-color: black;
}

ul.cards-split.transition li.card.card-1 {
  left: 730px;
  background-color: black;
}

ul.cards-split.transition li.card.card-2 {
  left: 380px;
  background-color: black;
}

ul.cards-split.transition li.card.card-3 {
  left: 30px;
  background-color: black;
}

ul.cards-split.transition li.card.card-4 {
  left: -330px;
  background-color: black;
}

@media screen and (max-width: 1200px) {
  ul.cards-split li.card {
    /* background: black; */

    overflow: hidden;
    height: 300px;
    width: 230px;
    border-radius: 10px;
    position: absolute;
    left: 220px;
    transition: all 2s cubic-bezier(0.63, 0.15, 0.03, 1.12);
  }

  ul.cards-split.transition li.card.card-1 {
    left: 590px;
    background-color: black;
  }

  ul.cards-split.transition li.card.card-2 {
    left: 340px;
    background-color: black;
  }

  ul.cards-split.transition li.card.card-3 {
    left: 90px;
    background-color: black;
  }

  ul.cards-split.transition li.card.card-4 {
    left: -160px;
    background-color: black;
  }
}

/* Apply as an Engg CSS */
.text-content-hide {
  display: none;
}

.slick_custom .slick-active.slick-current .text-content-hide {
  display: block !important;
  /* width: 400px !important; */
}

.wi {
  width: 350px !important;
  /* display: flex !important;
  justify-content: center !important;
  align-items: center !important; */
  /* margin-left: 40px !important; */
}

.slick_custom .slick-active.slick-current .wi {
  width: 640px !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  transition: transform ease-in-out;
}

/* .custom_slider.slick-slider{
  display: flex !important;
  justify-content: center !important;
  align-items:center  !important; 
 background-color: green !important; 
  box-sizing:  border-box !important;
  padding: 0px 0px 0px 0px !important;
  overflow: hidden !important;
 }  */
.slick_custom .slick-list {
  /* background-color: goldenrod !important; */
  margin: 0px -120px 0px 216px !important;
}

@media screen and (max-width: 1250px) {
  .slick_custom .slick-slider {
    /* background-color: #8D3F42 !important; */
    width: 1024px;
  }

  .wi {
    width: 350px !important;
    /* display: none !important; */
  }

  .slick_custom .slick-active.slick-current .wi {
    width: 500px !important;
    transition: transform ease-in-out;
  }

  .slick_custom .slick-list {
    /* background-color: goldenrod !important; */
    width: 100% !important;
    margin: 0px 0px 0px 0px !important;
  }

  .slick_custom .slick-track {
    /* background-color: aqua; */
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    /* margin-left: 150px !important; */
  }
}

@media screen and (max-width: 1024px) {
  .slick_custom .slick-slider {
    /* background-color: #8D3F42; */
    width: 1024px !important;
  }

  .wi {
    display: none !important;
  }

  .slick_custom .slick-active.slick-current .wi {
    width: 450px !important;
    transition: transform ease-in-out;
  }

  .slick_custom .slick-list {
    /* background-color: goldenrod !important; */
    width: 450px !important;
    margin: 0px 0px 0px 0px !important;
  }

  .slick_custom .slick-track {
    /* background-color: blueviolet; */
    margin-left: 0px !important;
    /* display: flex !important; */
    /* justify-content: center !important; */
    /* align-items: center !important; */
    /* width: 100% !important; */
    /* margin-left: 70px !important; */
  }

  .slick_custom .slick-slider {
    /* background-color: #8D3F42 !important; */
    width: 100% !important;
  }
}

@media screen and (max-width: 850px) {
  .slick_custom .slick-slider {
    /* background-color: #8D3F42; */
    width: 100%;
  }

  .wi {
    display: none !important;
  }

  .slick_custom .slick-active.slick-current .wi {
    width: 450px !important;
    transition: transform ease-in-out;
  }

  .slick_custom .slick-list {
    /* background-color: goldenrod !important; */
    width: 450px !important;
    margin: 0px 0px 0px 0px !important;
  }

  .slick_custom .slick-track {
    /* background-color: blueviolet; */
    margin-left: 0px !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    /* width: 100% !important; */
    /* margin-left: 160px !important; */
  }

  .slick_custom .slick-slider {
    /* background-color: #8D3F42 !important; */
    width: 100% !important;
  }
}

@media screen and (max-width: 480px) {
  .wi {
    display: none !important;
  }

  .slick_custom .slick-active.slick-current .wi {
    width: 100% !important;
    /* margin-right: 20px !important; */

    transition: transform ease-in-out !important;
  }

  .slick_custom .slick-list {
    /* background-color: goldenrod !important; */
    width: 100%;
    /* margin: 0px 20px 0px 0px !important; */
  }

  .slick_custom .slick-track {
    /* background-color: blueviolet; */
    margin-left: 0px !important;
    /* gap: 20px !important; */
  }

  .slick_custom .slick-slider {
    /* background-color: #8D3F42; */
    /* width: 100% !important; */
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    /* gap: 20px !important; */
    /* gap: 20px !important; */
  }

  .slick_custom.slick-active.slick-current .text-content-hide {
    display: block !important;
    width: 100% !important;
  }
}

@media screen and (max-width: 375px) {
  .slick_custom .slick-list {
    /* background-color: goldenrod !important; */
    width: 100%;
    /* margin: 0px 10px 0px 0px !important; */
  }

  .slick_custom .slick-track {
    /* background-color: blueviolet; */
    margin-left: 0px !important;
    /* gap: 10px !important; */
  }
}

.slick_custom .slick-dots {
  bottom: -50px !important;
  /* Adjust the position of the dots */
}

.slick_custom .slick-dots li {
  margin: 0 8px;
  /* Adjust the space between dots */
}

.slick_custom .slick-dots li button {
  font-size: 16px;
  /* Adjust the dot size */
  color: red;
  /* Adjust the dot color */
  opacity: 1;
  /* Adjust the dot opacity */
}

.slick_custom .slick-dots li.slick-active button {
  opacity: 1;
  /* Adjust the active dot opacity */
}

.slick_custom .slick-dots li.slick-active button:before {
  opacity: 1 !important;
  color: #bc7666 !important;
}

.slick_custom .slick-dots li button:before {
  font-family: "slick";
  font-size: 20px !important;
  line-height: 20px;
  position: absolute;
  top: 0;
  left: 0;
  width: 20px;
  height: 20px;
  content: "•";
  text-align: center;
  opacity: 1 !important;
  color: grey !important;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.heigh {
  height: 200px;
}

/* Laoder css */
.svg {
  position: absolute;
  width: 100%;
  height: 50%;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.svg {
  animation: fadeIn 0.5s ease-in infinite alternate;
}

.slick-slider {
  touch-action: auto !important;
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
}

.no-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.custom_slider {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
}

.custom_slider .slick-list {
  /* display: flex !important; */
  /* background-color: #38b6ff !important; */
  justify-content: center !important;
  align-items: center !important;
  /* height: 150px !important;  */
  overflow: hidden !important;
}

.imp {
  width: auto !important;
  border: none !important;
  outline: none !important;
}

.custom_slider .slick-slider {
  /* background-color: #8D3F42 !important; */
  width: 650px;
  height: 160px !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  /* height: 300px !important; */
}

/* .custom_slider .slick-prev::before{
  color: #BC7666;
  font-size: 25px !important;
  opacity: 1 !important;
  
} */

/* .custom_slider .slick-next::before{
 color: #BC7666;
  font-size: 25px !important;
  opacity: 1 !important;
  font-weight: 200 !important;
  background-color: #fff;
  
  
  
} */

/* .slick-prev::before, .slick-next::before {
  font-family: FontAwesome;
} */
/* .slick-prev::before { */
/* fa-arrow-circle-left */
/* content: "\f0a8"; */
/* } */
/* .slick-next::before { */
/* fa-arrow-circle-right */
/* content: "\f0a9"; */
/* } */
@media screen and (max-width: 1024px) {
  .custom_slider .slick-slider {
    /* background-color: #8D3F42; */
    width: 450px;
  }
}

@media screen and (min-width: 1024px) {
  .custom_slider .slick-slider {
    /* background-color: #8D3F42; */
    width: 600px;
  }
}

@media screen and (max-width: 480px) {
  .custom_slider .slick-slider {
    /* background-color: #8D3F42; */
    width: 370px;
  }
}

.animate-spin-fast {
  animation: spin 0.5s linear infinite;
}

.slick-prev,
.slick-next {
  display: none !important;
}

/*form*/

/* Hide scrollbar for Chrome, Safari and Opera */
.no-scrollbar::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.no-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.phoneinput {
  background: linear-gradient(to top, #241415, black) !important;
  text-decoration-color: #fff !important;
  font-family: "outfit" !important;
  font-size: large !important;
  border: none !important;
  border-radius: 8px !important;
  outline: none !important;
  width: 100% !important;
  height: 56px !important;
  padding-left: 80px !important;
}

.phoneinput:focus {
  outline: none !important;
  box-shadow: none !important;
}

@media (min-width: 1024px) {
  .phoneinput {
    height: 64px !important;
  }
}

.country-list {
  background-color: #000 !important;
  transition: background-color 0.3s ease !important;
}

.country:hover {
  background-color: #2b2a2a !important;
}

.flag-dropdown {
  background-color: #000 !important;
  border: none !important;
  border-radius: 8px !important;
}

.country.highlight {
  background-color: #080808 !important;
}

/* .selected-flag {
  background-color: #000 !important;
  border-radius: 8px !important;
  width: 60px !important;
  display: flex !important;
  align-items: center !important;
  justify-items: center !important;
} */

.selected-flag {
  background: linear-gradient(to top, #241415, black) !important;
  width: 60px !important;
  border-radius: 8px !important;
  border: none !important;
  outline: none !important;
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  color: #fff !important;
}

@keyframes slideInUp {
  0% {
    transform: translateY(100%);
    opacity: 0;
  }

  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideOutDown {
  0% {
    transform: translateY(0);
    opacity: 1;
  }

  100% {
    transform: translateY(100%);
    opacity: 0;
  }
}

.slide-in {
  animation: slideInUp 0.5s ease-in-out forwards;
}

.slide-out {
  animation: slideOutDown 0.3s ease-in-out forwards;
}

.z-index {
  z-index: 1000 !important;
}

.swal-custom-toast {
  background: linear-gradient(to top, #241415, black) !important;
  font-family: "outfit" !important;
  color: white !important;
  font-weight: 300 !important;
  border: 1px solid !important;
  border-color: #8d3f42 !important;
  padding: 10px 20px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
}

@keyframes spinGradient {
  0% {
    transform: rotate(360deg);
  }

  100% {
    transform: rotate(0deg);
  }
}

.animate-spin-gradient {
  animation: spinGradient 10s linear infinite;
}

.z-index {
  z-index: 1000 !important;
}

@keyframes slideDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }

  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(0);
    opacity: 1;
  }

  to {
    transform: translateY(-100%);
    opacity: 0;
  }
}

.menu-slide-down {
  animation: slideDown 0.5s ease forwards;
}

.menu-slide-up {
  animation: slideUp 0.5s ease forwards;
}


/* Container styles */
.resume-container {
  padding: 30px;
  background-color: black;
  max-width: 900px;
  margin: auto;
  color: #e0e0e0;
  border-radius: 10px;
  font-family: 'Outfit', sans-serif;
  position: relative;
}

/* Loading overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.dot-loader {
  display: flex;
  gap: 8px;
}

.dot-loader span {
  width: 12px;
  height: 12px;
  background-color: #ff6347;
  border-radius: 50%;
  animation: dot-bounce 1.2s infinite ease-in-out;
}

.dot-loader span:nth-child(2) {
  animation-delay: -0.4s;
}

.dot-loader span:nth-child(3) {
  animation-delay: -0.8s;
}

@keyframes dot-bounce {

  0%,
  80%,
  100% {
    transform: scale(0);
  }

  40% {
    transform: scale(1);
  }
}

/* Header */
.header-title {
  text-align: center;
  color: #f3f3f3;
  padding-bottom: inherit;
}

/* Form sections */
.form-group {
  margin-bottom: 20px;
  padding: 15px;
  background: linear-gradient(to bottom right, #101010, #101010, #481e1f);
  border-radius: 10px;
  border: 1px solid #ff6347;
}

.form-label {
  font-size: 16px;
  color: #fff;
}

.textarea {
  width: 100%;
  min-height: 120px;
  background: transparent;
  color: #eaeaea;
  font-size: 16px;
  border: none;
  font-family: 'Outfit', sans-serif;
  resize: none;
  overflow: hidden;
  outline: none;
}

/* Button styles */
.button {
  padding: 10px 15px;
  color: #ffffff;
  background: transparent;
  border: none;
  font-size: 14px;
  font-family: 'Outfit', sans-serif;
  cursor: pointer;
  border-radius: 5px;
}

.primary-button {
  padding: 12px 24px;
  background-color: #954948;
  margin-bottom: 20px;
  color: #fff;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  cursor: pointer;
  transition: 0.3s ease;
}

.primary-button:hover {
  background-color: #954948;
}

.file-list {
  margin-top: 15px;
  padding-left: 20px;
  color: #eaeaea;
}

.file-list li {
  margin-bottom: 10px;
  position: relative;
}

.remove-file-button {
  position: absolute;
  top: -5px;
  right: -5px;
  background: transparent;
  border: none;
  color: #ff6347;
  font-size: 18px;
  cursor: pointer;
}

.evaluation-container {
  margin-bottom: 20px;
  border: 1px solid #ff6347;
  padding: 15px;
  border-radius: 10px;
  background: linear-gradient(to bottom right, #101010, #101010, #481e1f);
}

.evaluation-label {
  font-size: 16px;
  color: #c0c0c0;
}

.evaluation-select {
  display: block;
  width: 100%;
  margin-top: 10px;
  padding: 10px;
  background: linear-gradient(to bottom right, #101010, #101010, #481e1f);
  color: #eaeaea;
  border: 1px solid #333;
  border-radius: 8px;
  font-size: 14px;
  font-family: 'Outfit', sans-serif;
  outline: none;
}

.evaluation-option {
  background-color: #481e1f;
  color: #fff;
}

/* Evaluation single file */

.evaluation-wrapper {
  margin-bottom: 20px;
}

.evaluation-title {
  color: #eaeaea;
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 20px;
  font-family: 'Outfit', sans-serif;
}

.evaluation-entry {
  padding-top: 5px;
  padding-bottom: 20px;
}

.evaluation-entry.with-border {
  border-top: 2px solid #ff6347;
}

.evaluation-file-title {
  color: #eaeaea;
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 15px;
  font-family: 'Outfit', sans-serif;
}

.evaluation-content {
  color: #e0e0e0;
  font-size: 15px;
  line-height: 1.8;
  white-space: pre-wrap;
  margin-top: 10px;
}

/* Add space for paragraphs in evaluation content */
.evaluation-content p {
  margin-bottom: 20px;
  /* Space after each paragraph */
}




/* Space between Response section */
.response-section {
  margin-top: 30px;
  margin-bottom: 20px;
}

.response-title {
  font-size: 18px;
  color: #eaeaea;
  font-weight: bold;
}

.swal-confirm-btn {
  background-color: green !important;
  color: white !important;
}

.swal-cancel-btn {
  background-color: red !important;
  color: white !important;
}

.evaluation-box {
  /* background-color: #fff; */
  padding: 12px 0px;
  font-size: 1rem;
  font-weight: bold;
  display: inline-block;
  color: #fff;
  margin-top: 16px;
}

@keyframes borderClockwise {
  0% {
    background-position: 0% 100%;
    clip-path: inset(0 100% 100% 0);
  }
  25% {
    background-position: 100% 100%;
    clip-path: inset(0 0 100% 0);
  }
  50% {
    background-position: 100% 0%;
    clip-path: inset(0 0 0 100%);
  }
  75% {
    background-position: 0% 0%;
    clip-path: inset(100% 0 0 0);
  }
  100% {
    background-position: 0% 100%;
    clip-path: inset(0 100% 100% 0);
  }
}

.border-clockwise::before {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: 12px;
  padding: 3px;
  background: linear-gradient(
    90deg,
    #6b443a,
    #481e1f,
    
  );
  background-size: 200% 200%;
  animation: borderClockwise 3s linear infinite;
  mask: linear-gradient(white 0 0) content-box, linear-gradient(white 0 0);
  mask-composite: exclude;
}

@layer utilities {
  .blur-sm {
    filter: blur(4px);
  }
  
  .blur-md {
    filter: blur(8px);
  }
}

@keyframes slowSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.slow-spin {
  animation: slowSpin 2s linear infinite; /* Slower rotation */
}
