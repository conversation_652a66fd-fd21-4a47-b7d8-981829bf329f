import "./globals.css";

import Head from 'next/head';
import { Inter } from "next/font/google";
import Link from "next/link";

const inter = Inter({ subsets: ["latin"] });

export const metadata = {
  title: "World-class software engineers, powered by AI | eremotehire",
  description: "We help companies hire and manage the best software engineers easily.",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <head>
      <meta
          // content="World-class software engineers, powered by AI | eremotehire"
          property="og:title"
        />
        <link rel="icon" href="../images/ERH_fav_16X16.png"/>
        <link rel="icon" type="image/png" sizes="32x32" href="../images/ERH_fav_32X32.png"/>
        <link rel="icon" type="image/png" sizes="192x192" href="../images/ERH_fav_192X192.png"/>
        <link rel="apple-touch-icon" sizes="180x180" href="../images/ERH_fav_180X180.png"/>
      </head>
      <body className={inter.className}>{children}</body>
    </html>
  );
}

