import React, { createContext, useContext, useEffect, useState } from 'react';

const ScreenShareContext = createContext<{
  stream: MediaStream | null;
  setStream: React.Dispatch<React.SetStateAction<MediaStream | null>>;
}>({
  stream: null,
  setStream: () => {},
});

export const useScreenShare = () => useContext(ScreenShareContext);

interface ScreenShareProviderProps {
  children: React.ReactNode;  
}

export const ScreenShareProvider: React.FC<ScreenShareProviderProps> = ({ children }) => {
  const [stream, setStream] = useState<MediaStream | null>(null);

  useEffect(() => {
    return () => {
      if (stream) {
        stream.getTracks().forEach((track) => track.stop());
      }
    };
  }, [stream]);

  return (
    <ScreenShareContext.Provider value={{ stream, setStream }}>
      {children}
    </ScreenShareContext.Provider>
  );
};
