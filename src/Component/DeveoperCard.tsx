import Image from "next/image";
import { Images } from "@/constant";
import React from "react";

interface DeveoperCardProps {
  image: any;
  name: any;
  role: any;
  country: any;
  rate: any;
}

const DeveoperCard: React.FC<DeveoperCardProps> = ({
  image,
  name,
  role,
  country,
  rate,
}) => {
  return (
    <div>
      <div className="relative w-[290px] h-[420px] flex justify-center items-center rounded-3xl border-contessa border overflow-hidden">
        <div className="absolute bg-gradient-to-tr from-[#000] via-[#bc7666] to-[#000] w-[500px] h-[500px] rounded-full animate-spin-gradient"></div>
        <div className="absolute bg-gradient-to-tr from-[#0c0b0ba8] via-[#0c0b0ba8] to-[#0b0b0b] h-full w-full flex justify-center items-center rounded-3xl">
          <div className="flex flex-col items-center justify-center text-center gap-y-5">
            <Image
              src={image}
              alt="/"
              width={700}
              height={700}
              className="h-44 w-44 rounded-full object-cover"
            />
            <div className="flex items-center gap-x-1">
              <h2 className="text-[28px] font-bold text-white">{name}</h2>
              <Image
                width={500}
                height={500}
                src={Images.blue_tick}
                alt="image_men"
                className="h-10 w-10"
              />
            </div>
            {role && (
              <p className="text-xl font-normal text-[#A3A2A2]">{role}</p>
            )}
            <div className="p-[1px] bg-gradient-to-r from-tosca to-contessa h-11 rounded-full inline-block">
              <div className="flex gap-x-1 justify-center items-center bg-[linear-gradient(to_top,_#241415,_black)] px-2 h-full rounded-full">
                <Image
                  width={500}
                  height={500}
                  src={Images.location}
                  alt="image_men"
                  className="h-5 w-5"
                />
                <h1 className="text-xl font-extralight text-[#A3A2A2]">
                  {country}
                </h1>
              </div>
            </div>
            <div className="text-2xl font-normal text-white">${rate}/hour</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DeveoperCard;
