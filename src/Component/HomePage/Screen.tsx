import { motion, useScroll, useTransform } from "framer-motion";
import { useRef, useState } from "react";
import { Images } from "@/constant";
import { LabelComponent } from "../../Component/label";
import NavbarComponent from "../../Component/Navbar";
import React from "react";
import { Strings } from "@/constant";

export default function Screen() {
  const containerRef = useRef(null);
  // const [replay, setReplay] = useState(true);

  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end end"],
  });

  const textScale = useTransform(scrollYProgress, [0, 1], [1, 1.2]);
  const imageValue = useTransform(scrollYProgress, [0, 1], ["-100%", "0%"]);

  // const container = {
  //   visible: {
  //     transition: {
  //       staggerChildren: 0.025,
  //     },
  //   },
  // };

  // const item = {
  //   hidden: {
  //     x: "-99%",
  //     transition: { ease: [0.455, 0.03, 0.515, 0.955], duration: 0.85 },
  //   },
  //   visible: {
  //     x: 0,
  //     transition: { ease: [0.455, 0.03, 0.515, 0.955], duration: 0.75 },
  //   },
  // };
  // const itemOne = {
  //   hidden: {
  //     x: "99%",

  //     transition: { ease: [0.455, 0.03, 0.515, 0.955], duration: 0.85 },
  //   },
  //   visible: {
  //     x: 0,
  //     transition: { ease: [0.455, 0.03, 0.515, 0.955], duration: 0.75 },
  //   },
  // };

  return (
    <section ref={containerRef} className=" bg-red-900- max-w-screen-2xl  ">
      <div
        style={{
          backgroundImage: `url('${Images.bg_image}') `,
          backgroundSize: "contain",
          backgroundPosition: "left",
          backgroundRepeat: "no-repeat",
          zIndex: "1",
        }}
        className="xl:h-[730px] lg:h-[585px] md:h-[475px] xs:h-[270px] "
      >
        <NavbarComponent />
        <div className="bg-yellow-800- flex justify-center items-center sm:h-[40vh]- md:h-[40vh]   xl:h-[450px] lg:h-[55vh] xs:h-[20vh] sm:h-[45vh]- ">
          <motion.div
          // initial="hidden"
          // animate={replay ? "visible" : "hidden"}
          // variants={container}
          >
            <div className="pulse bg-transparent  font-outfit leading-normal font-bold  flex flex-col  items-center  justify-center">
              <motion.div style={{ display: "block" }}
              //  variants={item}
              >
                <LabelComponent
                  className=" xl:text-[49px] md:text-[36px] sm:text-[30px] xs:text-[16px] flex flex-col items-center   xs:text-white  "
                  label={Strings.Hire_dedicated}
                />
              </motion.div>
              <motion.div style={{ display: "block" }}
              // variants={itemOne}
              >
                <LabelComponent
                  className="xl:text-[60px] md:text-[36px] sm:text-[30px] xs:text-[22px] flex flex-col items-center text"
                  label={Strings.Domain_you}
                />
              </motion.div>
            </div>
          </motion.div>
        </div>
        {/* <div
          style={{
            backgroundImage: `url('${Images.shadow}')`,
            zIndex: -5,
            backgroundSize: "cover",
            backgroundPosition: "center",
          }}
          className="absolute xl:top-[34.5rem] 
          lg:w-[400px] lg:h-[200px] lg:top-[27.5rem]
           md:h-[200px] md:top-[20.5rem]
           xs:hidden md:block
         
          opacity-[0.3] drop-shadow-[0_100px_50px_rgba(143,66,66,1)] "
        ></div>
        <div
          style={{
            backgroundImage: `url('${Images.shadow}')`,
            zIndex: -5,
            backgroundSize: "cover",
            backgroundPosition: "center",
          }}
          className="lg:w-[450px] lg:h-[200px] absolute xl:top-[48.5rem] 2xl:top-[48.5rem] lg:top-[34.5rem]  lg:right-[-10px] 2xl:right-[500px]
           md:h-[200px] md:top-[28.5rem] md:right-[0px]
           xs:hidden md:block

            opacity-[0.35] "
        ></div> */}
      </div>
    </section>
  );
}
