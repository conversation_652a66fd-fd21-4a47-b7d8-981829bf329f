import "../../app/globals.css";

import React, { useEffect, useState } from "react";

import AnimatedCircle from "@/Component/AnimatedCircle";
import CircleRounded from "@/Component/CircleRounded";
import Cookie from "@/Component/cookies";
import CountComponent from "@/Component/CountCompent";
import { Footer } from "@/Component/footer";
import ImageHoverComponent from "@/Component/HomePage/imagehover";
import ImageSliderComponent from "@/Component/HomePage/ImageSlider";
import Loader from "@/Component/loader";
import MarqueeUp from "@/Component/HomePage/Imageup";
import Screen from "@/Component/HomePage/Screen";
import Slider from "@/Component/HomePage/Slider";
import Slidertwo from "@/Component/HomePage/SliderTwo";
import { trackPageView } from "@/utils/helper";

export default function HomeScreen({
  resources,
  logos,
  openToWork,
  communityDetails,
}: any) {
  const [loading, setLoading] = useState(false);

  // useEffect(() => {
  //   const timer = setTimeout(() => {
  //     setLoading(false);
  //   }, 500);

  //   return () => clearTimeout(timer);
  // }, []);

  useEffect(() => {
    trackPageView("/home-page", "Home Page");
  }, []);
  // useEffect(() => {
  //   // Simulate loading for 2 seconds (adjust as needed)
  //   const timer = setTimeout(() => {
  //     setLoading(false);
  //   }, 500);

  //   return () => clearTimeout(timer);
  // }, []);

  return (
    <div className="max-w-screen-2xl m-auto">
      {loading ? (
        // Loader component while content is loading
        <Loader />
      ) : (
        // Content of your page
        <>
          <div className="">
            <Cookie />
          </div>
          <Screen />
          <div className="xl:mt-[-200px]  md:-mt-20">
            <CountComponent />
          </div>
          <div className="">
            <Slider resources={resources} />
          </div>
          <div className="flex justify-center">
            <ImageSliderComponent logos={logos} />
          </div>
          <div className="">
            <ImageHoverComponent openToWork={openToWork} />
          </div>
          <div className="">
            <MarqueeUp />
          </div>
          <div className="">
            <CircleRounded />
          </div>
          <div>
            <AnimatedCircle />
          </div>
          <div>
            <Slidertwo community={communityDetails} />
          </div>

          <footer>
            <Footer />
          </footer>
        </>
      )}
    </div>
  );
}
