import {
  ArrowSmallLeftIcon,
  ArrowSmallRightIcon,
} from "@heroicons/react/24/outline";
import Hiretopengineer, { toggleModal } from "../../Component/Hiretopengineer";
import { motion, useTransform, useViewportScroll } from "framer-motion";
import { usePathname, useRouter } from "next/navigation";
import { useRef, useState } from "react";

import Image from "next/image";
import { Images } from "@/constant";
import { LabelComponent } from "../../Component/label";
import React from "react";
import Slider from "react-slick";
import { Strings } from "@/constant";
import axios from "axios";

interface Resource {
  id: number;
  profilePicture: string;
  description: string;
  fullName: string;
  paragraph: string;
  Title: string;
  position: string;
}

const Slidertwo = ({ community }: any) => {
  const containerRef = useRef(null);
  const sliderRef = useRef<Slider>(null);
  const router = useRouter();
  const pathName = usePathname();
  const [isOpen, setIsOpen] = useState(false);
  const [data, setData] = useState<Resource[]>(community);

  const handleButtonClick = () => {
    toggleModal(isOpen, setIsOpen);
  };
  const settings: any = {
    dots: false,
    infinite: true,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    nextArrow: null,
    prevArrow: null,
    swipe: false,

    // swipeToSlide: false,
    arrows: false, // Hide both "previous" and "next" arrows
  };
  // const [title, setTitle] = useState(data.imgeDataSecond[0]); // Set initial title
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isHovered, setIsHovered] = useState(false);
  const [set, setColor] = useState(false);
  const [displayOrder, setDisplayOrder] = useState([1, 0, 2]);
  // const imageData = data.imgeDataSecond[currentIndex];

  const { scrollYProgress } = useViewportScroll();
  const scale = useTransform(scrollYProgress, [0, 0.9], [0.5, 1]);

  const activeImageClass =
    "transform scale-150 opacity-100 bg-red-400- width-full z-[1]";

  const handleMouseOver = () => {
    setIsHovered(true);
  };

  const handleMouseOut = () => {
    setIsHovered(false);
  };

  const movePrev = () => {
    setCurrentIndex((prevIndex) => (prevIndex - 1 + data.length) % data.length);
    const newData = [...data];
    const temp = newData[newData.length - 1];
    newData.pop();
    newData.unshift(temp);
    setData(newData);

    // Update displayOrder based on the new order of data
    const newDisplayOrder = displayOrder.map(
      (index) => (index - 1 + data.length) % data.length
    );
    setDisplayOrder(newDisplayOrder);

    (sliderRef.current as any)?.slickPrev();
    setColor(true);
  };

  const moveNext = () => {
    setCurrentIndex((prevIndex) => (prevIndex + 1) % data.length);
    const newData = [...data];
    const temp = newData[0];
    newData.shift();
    newData.push(temp);
    setData(newData);

    // Update displayOrder based on the new order of data
    const newDisplayOrder = displayOrder.map(
      (index) => (index + 1) % data.length
    );
    setDisplayOrder(newDisplayOrder);

    (sliderRef.current as any)?.slickNext();
    setColor(false);
  };

  if (data.length === 0) {
    return null;
  }

  const images = data.map((item) => item.profilePicture);
  // console.log(images, "this is ");

  // Initial order: 1, 2, 0
  // const displayOrder = [1, 2, 0];

  return (
    <>
      <div className="">
        <section ref={containerRef}>
          {data?.length > 0 ? (
            <>
              <div
                className=" bg-red-900-  relative xs:mt-[50px]- xs:block bg-red-500- md:mt-[100px] xs:mt-[100px] 
            h-[790px]- sm:h-full-   "
              >
                <div className=" relative xs:text-[30px] sm:text-[40px] my-[50px]  text-center font-bold text-white font-outfit not-italic leading-normal md:hidden xs:block ">
                  <LabelComponent label={Strings.Become} />
                  <div className="flex justify-center items-center ">
                    <LabelComponent label={Strings.grow} />
                    <LabelComponent
                      className="text-Contessa ml-2"
                      label={Strings.community}
                    />
                  </div>
                </div>
                <motion.div
                  //  style ={{ scale }}

                  className=" xs:mt-[50px]- flex justify-around items-center  xs:flex-col sm:flex-row   snap-x snap-mandatory touch-pan-x- z-0"
                >
                  <div className="img-inner flex">
                    {data.map((resource, index) => {
                      const isActive = index === currentIndex;
                      const imageClass = isActive ? activeImageClass : "";
                      return (
                        <div
                          key={index}
                          className={` text-center snap-start flex ${
                            index === currentIndex ? "block" : "hidden"
                          }`}
                        >
                          <div className="xl:ml-[0px]  md:mr-[0px] xs:mr-0 flex justify-center items-center xs:py-10 sm:py-0     ">
                            <div
                              className={` rotate-45 rounded-[10px] xl:w-[120px] xl:h-[120px] lg:w-[140px] lg:h-[140px] md:w-[100px] md:h-[100px] xs:w-[100px] xs:h-[100px] flex justify-center items-center overflow-hidden 
                          `}
                            >
                              <Image
                                src={images[0]}
                                fill
                                alt="/"
                                className={`flex transform scale-[1.33] rotate-[315deg] max-w-none object-fit max-h-none w-auto h-auto ${
                                  resource.id === 0 ? "z-30" : ""
                                }`}
                              />
                            </div>
                            <div
                              className={` rotate-45 rounded-[10px] ${imageClass} xl:w-[110px] xl:h-[110px] lg:w-[110px] lg:h-[110px] md:w-[100px] md:h-[100px] xs:w-[100px] xs:h-[100px] flex justify-center items-center overflow-hidden   `}
                            >
                              <Image
                                src={images[1]}
                                alt={`Image ${index}`}
                                fill
                                // width={100}
                                // height={100}
                                quality={100}
                                className="transform rotate-[315deg]  scale-[1.33]   max-w-none- object-fit max-h-none- w-full h-full"
                              />
                            </div>
                            <div
                              className={`  rotate-45 -z-10- opacity-[0.6]- rounded-[10px] xl:w-[120px] xl:h-[120px] lg:w-[140px] lg:h-[140px]  md:w-[100px] md:h-[100px] xs:w-[100px] xs:h-[100px]  flex justify-center items-center overflow-hidden`}
                            >
                              <Image
                                src={images[2]}
                                alt={`Image ${index}`}
                                fill
                                className={`flex transform scale-[1.33] rotate-[315deg] max-w-none object-fit overflow-hidden max-h-none w-auto h-auto ${
                                  resource.id === 0 ? "z-30" : ""
                                }`}
                              />
                            </div>
                          </div>
                          <div className="lg:block xs:hidden">
                            <Image
                              src={Images.elipse}
                              alt="shadow logo"
                              width={400}
                              height={400}
                              className=" opacity-[0.2] absolute top-[70px] xl:left-[120px] lg:left-[50px] -z-20"
                            />
                          </div>
                        </div>
                      );
                    })}
                  </div>

                  <div className="bg-yellow-500-">
                    <div className="xl:w-[650px] xl:pl-[100px] xl:h-[500px] lg:w-[490px] lg:h-[490px] md:w-[400px] md:h-[400px] xs:w-[300px] xs:h-[440px]- ">
                      <motion.div className="img-inner">
                        <div className="xl:text-[48px] lg:text-[40px] md:text-[35px] font-bold text-white font-outfit not-italic leading-normal sm:block xs:hidden">
                          <LabelComponent label={Strings.Become} />
                          <div className="flex ">
                            <LabelComponent
                              className="border-b border-Contessa pb-4"
                              label={Strings.grow}
                            />
                            <LabelComponent
                              className="text-Contessa ml-2"
                              label={Strings.community}
                            />
                          </div>
                        </div>
                        <Slider
                          ref={sliderRef}
                          {...settings}
                          afterChange={(index: any) => setCurrentIndex(index)}
                        >
                          {displayOrder.map((desiredIndex, displayIndex) => {
                            const resource = data[desiredIndex];
                            // console.log(resource,"resource")

                            return (
                              <div key={displayIndex}>
                                <div>
                                  <p className="md:mt-[70px] xs:mt-4 font-light font-outfit text-[17px] not-italic leading-normal text-white">
                                    {resource.description}
                                  </p>
                                  <p className="md:mt-[70px] xs:mt-4 font-outfit text-white font-bold not-italic leading-normal text-[24px]">
                                    {resource.fullName}
                                  </p>
                                  <div className="flex items-center">
                                    <span className="flex justify-center items-center w-[20px] h-[1px] bg-Contessa mx-1"></span>
                                    <span className="font-outfit text-Contessa font-extralight not-italic leading-normal text-[20px]">
                                      {resource.position}
                                    </span>
                                  </div>
                                </div>
                              </div>
                            );
                          })}
                        </Slider>
                      </motion.div>
                    </div>

                    <div className="bg-white- md:mt-[100px] xs:mt-[50px] sm:my-0 pl-[100px]">
                      <motion.div
                        className="img-inner"
                        // style={
                        //   !windows
                        //     ? { translateY: imageValueOn }
                        //     : { translateX: imageValueOne }
                        // }
                      >
                        <div className="  flex items-center justify-end  lg:mr-[150px]- lg:my-[20px]- xs:my-[120px]- ">
                          <button
                            onClick={movePrev}
                            className=" rounded-[100%]  bg-gradient-to-b from-Tosca to-Contessa text-white w-10 h-10 flex items-center justify-center text-center "
                          >
                            <ArrowSmallLeftIcon className=" text-white font-bold w-6 h-5" />
                          </button>
                          <button
                            onClick={moveNext}
                            className=" rounded-[100%] ml-4 bg-gradient-to-b from-Tosca to-Contessa text-white w-10 h-10 flex items-center justify-center mr-4"
                          >
                            <ArrowSmallRightIcon className="font-bold text-white w-6 h-5" />
                            {/* <span className="sr-only">Next</span> */}
                          </button>
                        </div>
                      </motion.div>
                    </div>
                  </div>
                </motion.div>
              </div>
            </>
          ) : (
            <p className="text-center"></p>
          )}
        </section>
      </div>
      <section>
        <div className="flex justify-center  items-center xs:my-[50px]- md:my-[100px] xs:my-[100px] md:mt-[20px]- bg-pink-600- ">
          <div className=" hover:border-none    hover:drop-shadow-[0_17px_14px_rgba(143,66,66,1)] hover:transition-all hover:duration-300   relative rounded-[100%] md:h-[600px] md:w-[600px]  sm:w-[450px] sm:h-[450px] xs:w-[300px] xs:h-[300px] flex justify-center items-center   ">
            <div
              className="absolute -inset-px  bg-gradient-to-b from-Contessa to-Tosca  rounded-[100%]"
              aria-hidden="true"
            ></div>
            <div
              className="group  absolute inset-0 bg-black rounded-[100%] flex justify-center items-center flex-col transition-all duration-700 transform translate-y-[-3px]"
              aria-hidden="true"
            >
              <div
                onMouseOver={handleMouseOver}
                onMouseOut={handleMouseOut}
                className={`${
                  isHovered ? "translate-y-[-60px]" : "translate-y-[0px]"
                } transition-all duration-700 transform flex justify-center items-center flex-col xs:group-hover:translate-y-[-12px] md:group-hover:translate-y-[-30px] text-white`}
              >
                <LabelComponent
                  label={"Hire "}
                  className="font-outfit font-bold xs:text-[30px] md:text-[70px] text-center justify-center flex"
                />
                <LabelComponent
                  label="Remote "
                  className="font-outfit font-bold xs:text-[30px] md:text-[70px] text-center justify-center flex"
                />
                <LabelComponent
                  label=" Developer"
                  className="font-outfit font-bold xs:text-[30px] md:text-[70px] text-center justify-center flex"
                />
              </div>
              {/* <ButtonComponent className="  font-outfit bg-gradient-to-t from-Tosca to-Contessa lg:mx-18   hidden group-hover:flex text-white  sm:text-[36px] xs:text-[20px] font-normal border-none rounded-[40px]  md:py-10 sm:px-[3rem] xs:px-[1rem] xs:py-[20px]  xs:items-center   ">
            {Strings.developers}
          </ButtonComponent> */}
              <button
                onClick={handleButtonClick}
                className="font-outfit bg-gradient-to-t from-Contessa to-Tosca hover:bg-gradient-to-b hover:from-Contessa hover:to-Tosca transition-all transform hover:scale-110 duration-700 lg:mx-18 ease-in-out scale-100 
               hidden- -mb-10 group-hover:mb-0 opacity-0 group-hover:translate-y-[-20px]- group-hover:opacity-100 group-hover:flex text-white font-normal border-none rounded-[40px]  md:py-5 sm:px-[3rem] xs:px-[1rem] xs:py-[10px]  xs:items-center"
              >
                <LabelComponent
                  label={Strings.developers}
                  className="font-outfit font-light text-xl text-white sm:text-[36px] xs:text-[20px] "
                />
              </button>
            </div>
          </div>
          <div className="text-left">
            <Hiretopengineer isOpen={isOpen} setIsOpen={setIsOpen} />
          </div>
        </div>
      </section>
    </>
  );
};

export default Slidertwo;
