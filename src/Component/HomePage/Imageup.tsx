import { Images, Strings } from "@/constant";
import { motion, useTransform, useViewportScroll } from "framer-motion";

import Image from "next/image";
import { LabelComponent } from "../../Component/label";
import React from "react";

const MarqueeUp: React.FC = () => {
  const { scrollYProgress } = useViewportScroll();

  const scale = useTransform(scrollYProgress, [0, 0.8], [0.4, 1.4]);

  return (
    <div className=" bg-yellow-800- xs:my-[40px]- sm:my-0   lg:my-[100px]- xl:my-[150px]- md:my-[50px]-     ">
      <div className="text-center relative   ">
        <LabelComponent
          className="xl:text-[45px] lg:text-[35px]  md:text-[30px] xs:text-[25px] text-white font-semibold not-italic  leading-normal pt-[100px]-"
          label={Strings.FOUR}
        />
        <LabelComponent
          className="xl:text-[70px] lg:text-[50px] -z-10- md:text-[30px] xs:text-[30px] text-Contessa font-bold not-italic leading-normal-  border-b pb-[0px] border-Contessa mt-[100px]-"
          label={Strings.HIRE}
        />

        <div className=" absolute lg:block xs:hidden w-[900px] xl:h-[600px] lg:h-[500px]  xl:-top-[230px]  xl:left-[210px] lg:left-[60px] lg:-top-[200px] mb-[100px]-  ">
          <Image
            src={Images.Shadow}
            alt="/"
            fill
            className=" opacity-[0.2] -z-20-"
          />
        </div>
      </div>
      <div className="flex mt-16 xs:justify-center lg:justify-between xs:mx-[50px] lg:flex-row xs:flex-col xs:gap-y-12 lg:gap-y-0">
        <div className="flex justify-center items-center flex-col">
          <Image
            src={Images.TELL_US}
            width={600}
            height={600}
            alt="img"
            className="border border-contessa rounded-full w-[80px] h-[80px] flex justify-center items-center p-4"
          />
          <p className="border-l-2 border-dashed border-contessa h-[150px]" />
          <LabelComponent
            className="font-semibold xs:text-xl md:text-3xl lg:text-2xl xl:text-3xl text-white"
            label={Strings.TEL}
          />
        </div>
        <div className="flex justify-center items-center xs:flex-col lg:flex-col-reverse">
          <Image
            src={Images.MATCH_DEVELOPER}
            width={600}
            height={600}
            alt="img"
            className="border border-contessa rounded-full w-[80px] h-[80px] flex justify-center items-center p-4"
          />
          <p className="border-l-2 border-dashed border-contessa h-[150px]" />
          <LabelComponent
            className="font-semibold xs:text-xl md:text-3xl lg:text-2xl xl:text-3xl text-white"
            label={Strings.GETT}
          />
        </div>
        <div className="flex justify-center items-center flex-col">
          <Image
            src={Images.TEAM_WORK}
            width={600}
            height={600}
            alt="img"
            className="border border-contessa rounded-full w-[80px] h-[80px] flex justify-center items-center p-4"
          />
          <p className="border-l-2 border-dashed border-contessa h-[150px]" />
          <LabelComponent
            className="font-semibold xs:text-xl md:text-3xl lg:text-2xl xl:text-3xl text-white"
            label={Strings.BEING}
          />
        </div>
        <div className="flex justify-center items-center xs:flex-col lg:flex-col-reverse">
          <Image
            src={Images.CHOOSE}
            width={600}
            height={600}
            alt="img"
            className="border border-contessa rounded-full w-[80px] h-[80px] flex justify-center items-center p-4"
          />
          <p className="border-l-2 border-dashed border-contessa h-[150px]" />
          <LabelComponent
            className="font-semibold xs:text-xl md:text-3xl lg:text-2xl xl:text-3xl text-white"
            label={Strings.CHOOSE}
          />
        </div>
      </div>
    </div>
  );
};

export default MarqueeUp;
