import Image from "next/image";
import { Images } from "@/constant";
import { LabelComponent } from "../../Component/label";
import React, { useEffect, useState } from "react";
import { Strings } from "@/constant";
import axios from "axios";
import { StaticImport } from "next/dist/shared/lib/get-img-props";
interface LogoData {
  id: number;
  logo: string;
}

const ImageSliderComponent = ({ logos }: { logos: LogoData[] }) => {
  return (
    <div className=" bg-red-600- xs:mt-[30px]- xl:my-[50px]- xs:my-[30px]-">
      {logos?.length > 0 ? (
        <>
          <div className="  text-center font-outfit font-light text-white leading-normal not-italic text-[28px] xl:mt-[90px]- xs:mt-[30px]- ">
            <LabelComponent label={Strings.trust} />
          </div>
          <div className=" mt-[30px] bg-red-800- xl:w-full md:w-[700px] lg:w-[900px] xs:w-[360px] sm:w-[550px] bg-red-900- h-[100px]  flex items-center  flex-row justify-center ">
            <div className="relative overflow-x-hidden  xs:mx-[30px] sm:mx-[600px]- xl:mx-[200px] flex flex-row justify-center items-center">
              <div className="md:pt-[60px]- xs:py-4  flex items-center- justify-center-  animate-marquee whitespace-nowrap ">
                {logos.map(
                  (
                    logo: { logo: string | StaticImport; id: any },
                    index: React.Key | null | undefined
                  ) => {
                    // Log the logo.id outside the JSX
                    return (
                      <div
                        key={index}
                        className="md:mx-5 lg:mx-[2.25rem] xs:mx-2 flex justify-center items-center w-[200px] bg-red-900-"
                      >
                        <Image
                          src={logo.logo}
                          alt={`Logo ${logo.id}`}
                          width={100}
                          height={100}
                          className={`bg-green-800- ]  aspect-[3/2] object-contain ${
                            index === 0
                              ? "bg-red-900- h-[160px] w-[160px]"
                              : index === 1
                              ? "bg-yellow-500- w-[130px] h-[130px]"
                              : index === 2
                              ? "bg-red-600- h-[160px] w-[160px]"
                              : index === 3
                              ? "bg-pink-300- h-[160px] w-[160px]"
                              : ""
                          }`}
                        />
                      </div>
                    );
                  }
                )}
              </div>
              <div className="absolute flex  top-0 md:pt-[60px]- xs:py-4 animate-marquee2 whitespace-nowrap">
                {logos.map((logo, index) => {
                  // console.log(logo.id); // Log the logo.id outside the JSX
                  return (
                    <div
                      key={index}
                      className="md:mx-5 lg:mx-[2.25rem] xs:mx-2 flex justify-center items-center w-[200px] bg-red-900-"
                    >
                      <Image
                        src={logo.logo}
                        alt={`Logo ${logo.id}`}
                        width={100}
                        height={100}
                        className={`bg-green-800- ]  aspect-[3/2] object-contain ${
                          index === 0
                            ? "bg-red-900- h-[160px] w-[160px]"
                            : index === 1
                            ? "bg-yellow-500- w-[130px] h-[130px]"
                            : index === 2
                            ? "bg-red-600- h-[160px] w-[160px]"
                            : index === 3
                            ? "bg-pink-300- h-[160px] w-[160px]"
                            : ""
                        }`}
                      />
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </>
      ) : (
        <p className="text-center"></p>
      )}
    </div>
  );
};

export default ImageSliderComponent;
