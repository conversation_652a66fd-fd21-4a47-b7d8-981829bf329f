"use client";

import "slick-carousel/slick/slick-theme.css";
import "slick-carousel/slick/slick.css";
import "../../app/globals.css";

import { LeftOutlined, RightOutlined } from "@ant-design/icons";
import { useRef, useState } from "react";

import Image from "next/image";
import { LabelComponent } from "../../Component/label";
import Slider from "react-slick";
import { StaticImport } from "next/dist/shared/lib/get-img-props";
import { Strings } from "@/constant";

interface DeveloperData {
  profilePicture: string | StaticImport;
  summary: string;
  firstName: string;
  lastName: string;
  designation: string;
  country: string;
}

const ImageHoverComponent = ({ openToWork }: any) => {
  const sliderRef = useRef<Slider | null>(null);
  const [title, setTitle] = useState<DeveloperData | null>(
    openToWork.length > 0 ? openToWork[0] : null
  );
  const [active, setActive] = useState(0);

  const settings: any = {
    dots: false,
    infinite: true,
    centerMode: true,
    centerPadding: "0px",
    slidesToShow: 1,
    slidesToScroll: 1,
    variableWidth: true,
    arrows: false,
    swipeToSlide: true,
    touchMove: true,
    autoplaySpeed: 3000,
    speed: 1000,
    autoplay: true,
    swipe: true,
    pauseOnHover: true,
    afterChange: (current: number) => {
      setActive(current);
      setTitle(openToWork[current]);
    },
  };

  return (
    <div className="bg-pink-400- md:mt-[100px] md:mb-[40px] xs:mt-[50px] xs:mb-[40px]">
      <div className="bg-green-300- h-[400px] text-center xs:mx-5 md:mx-[15%] lg:mx-[25%]">
        {openToWork?.length > 0 ? (
          <>
            <div className="text-white lg:text-[46px] md:text-4xl xs:text-xl font-bold">
              <div>
                {Strings.Dev}
                <span className="text-Contessa mx-2">{Strings.hire}</span>
                {Strings.now}
              </div>
            </div>
            <div className="flex items-center bg-yellow-200- gap-x-2 my-4">
              <div
                onClick={() => (sliderRef.current as any)?.slickPrev()}
                className="bg-gradient-to-b from-Contessa to-Tosca flex justify-center items-center rounded-full h-6 w-7 md:w-[26px] cursor-pointer"
              >
                <LeftOutlined className="text-[10px] text-white" />
              </div>
              <div className="overflow-hidden w-full bg-red-300- ">
                <Slider ref={sliderRef} {...settings}>
                  {openToWork?.map((CurrEle: DeveloperData, index: number) => (
                    <div
                      key={index}
                      className="px-[9px] md:px-[16px] lg:px-[22px] xl:px-[18px] my-3"
                    >
                      <Image
                        className={`lg:w-[100px] lg:h-[106px] xs:w-[70px] xs:h-[74px] cursor-pointer transition-transform duration-200 transform object-cover md:hover:scale-110 xs:hover:scale-100 
                          ${
                            index !== active
                              ? "opacity-50 rounded-[10px]"
                              : "rounded-[10px] lg:w-[100px] lg:h-[106px] ease-in-out duration-500"
                          }`}
                        onMouseOver={() => {
                          setActive(index), setTitle(CurrEle);
                        }}
                        objectFit={"contain"}
                        src={CurrEle.profilePicture}
                        alt=""
                        width={500}
                        height={500}
                      />
                    </div>
                  ))}
                </Slider>
              </div>
              <div
                onClick={() => (sliderRef.current as any)?.slickNext()}
                className="bg-gradient-to-b from-Contessa to-Tosca flex justify-center items-center rounded-full h-6 w-7 md:w-[26px] cursor-pointer"
              >
                <RightOutlined className="text-[10px] text-white" />
              </div>
            </div>
            <div className="text-white lg:text-xl xs:text-base font-thin">
              <LabelComponent label={title?.summary} />
            </div>
            <div className="flex  justify-center items-center mt-8 lg:text-xl xs:text-base font-extralight">
              <LabelComponent
                className=" text-white"
                label={title?.firstName + " " + title?.lastName}
              />
              <span className="text-[#ffffff80] lg:text-[27px] mx-1">-</span>
              <LabelComponent
                className="text-[#ffffff80]"
                label={title?.designation}
              />
            </div>
          </>
        ) : (
          <div></div>
        )}
      </div>
    </div>
  );
};

export default ImageHoverComponent;
