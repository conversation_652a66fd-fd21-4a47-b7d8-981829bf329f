/* eslint-disable @next/next/no-img-element */
"use client";

import {
  ArrowSmallLeftIcon,
  ArrowSmallRightIcon,
} from "@heroicons/react/24/outline";
import { Images, Strings } from "@/constant";
import { motion, useTransform, useViewportScroll } from "framer-motion";
import { useEffect, useRef, useState } from "react";
import { LabelComponent } from "../../Component/label";
import React from "react";
import { Image } from "antd";

interface Resource {
  img: string;
  profile: string;
  fullName: string;
  position: string;
  message: string;
}

// interface SliderProps {}

const Slider: React.FC<{ resources: Resource[] }> = ({ resources }) => {
  const itemWidth = 330;

  const [currentIndex, setCurrentIndex] = useState<number>(0);

  const { scrollYProgress } = useViewportScroll();
  const scale = useTransform(scrollYProgress, [0, 1], [0.8, 2]);

  const carousel = useRef<HTMLDivElement>(null);

  const movePrev = () => {
    if (currentIndex > 0) {
      setCurrentIndex((prevState) => prevState - 1);
    }
  };

  const moveNext = () => {
    if (currentIndex < resources.length - 1) {
      setCurrentIndex((prevState) => prevState + 1);
    }
  };

  useEffect(() => {
    if (carousel.current !== null) {
      carousel.current.scrollLeft = itemWidth * currentIndex;
    }
  }, [currentIndex]);

  return (
    <section className="bg-yellow-900- md:my-[100px] xs:mt-[100px] xs:mb-[50px] sm:mb-0 sm:mt-0">
      <div>
        {resources?.length > 0 ? (
          <>
            <div className="relative carousel sm:flex justify-center items-center h-[90vh]- sm:w-full mt-[5rem]- xs:block    ">
              <div className=" relative sm:w-[48%] xs:w-full  flex justify-center items-center sm:h-[32rem] bg-red-700- xs:h-[100px] ">
                <div className="xs:text-[40px]  lg:text-[60px] leading-normal not-italic font-bold font-outfit xs:mt-6 sm:mt-0">
                  <div className="relative">
                    <motion.div
                      className=" text-white"
                      // style={
                      //   !windows
                      //     ? { translateY: imageValueOn }
                      //     : { translateX: imageValue }
                      // }
                    >
                      {Strings.what}
                      <span className="text-Contessa ml-[12px]">
                        {Strings.Client}
                      </span>
                      <LabelComponent label={Strings.Think} />
                      <LabelComponent label={Strings.About} />
                      <div className="lg:block xs:hidden absolute top-[-50px] ">
                        <Image
                          src={Images.elipse}
                          alt="/"
                          preview={false}
                          className=" opacity-[0.2] -z-20 "
                        />
                      </div>
                    </motion.div>
                  </div>
                  <div>
                    <div className="border-b pb-[30px] border-Contessa w-[180px]"></div>
                  </div>
                </div>
              </div>

              <motion.div
                // style={{ scale }}
                ref={carousel}
                className="  xs:ml-4 sm:ml-0 xs:mt-[6rem] sm:mt-0  flex  sm:w-[60%]  sm:gap-[50px] md:gap-[70px] xs:w-full- xs:gap-[40px] overflow-hidden scroll-smooth snap-x snap-mandatory touch-pan-x- z-0 overflow-x-scroll no-scrollbar "
              >
                {/* <div className="flex-  md:gap-[70px]- xs:gap-[40px]-"> */}
                {resources.map((resource, index) => {
                  return (
                    <div
                      key={index}
                      className="text-center flex w-[full]   rounded-2xl p-[2px] bg-gradient-to-b from-Contessa  to-Tosca hover:bg-gradient-to-t hover:from-Contessa hover:to-orange-200 
                      h-[460px] snap-start "
                    >
                      <div className="xs:w-[18rem] md:w-[17rem] xl:w-[290px] px-[1px] h-full rounded-[15px] bg-black">
                        <Image
                          className="w-full h-[10px] mt-[50px]"
                          src={Images.DOUBLE_COMMA}
                          alt="/"
                          preview={false}
                          // width={100}
                          // height={100}
                        />
                        <div className="w-full flex justify-center items-center">
                          <Image
                            className=" mt-8 rounded-full"
                            src={resource.profile}
                            preview={false}
                            // Set quality to 100% for maximum quality
                            alt="/"
                            width={130}
                            height={130}
                          />
                        </div>
                        <h2 className="mt-10 font-outfit bg-gradient-to-b from-Contessa to-Tosca font-normal text-transparent bg-clip-text xs:text-[26px] md:text-[26px] lg:text-[30px] leading-normal">
                          {resource.fullName}
                        </h2>
                        <p className="font-outfit font-light xs:text-[14px] md:text-[16px] lg:text-[16px] leading-normal not-italic">
                          {resource.position}
                        </p>
                        <p className="text-white mt-4 font-opensans w-fixed xs:text-[14px] md:text-[14px] lg:text-[13px] not-italic font-thin leading-normal inline-block px-[10px]">
                          {resource.message}
                        </p>
                      </div>
                    </div>
                  );
                })}
                {/* </div> */}
              </motion.div>
            </div>
            <div className="sm:mt-6 flex items-center justify-end text-right mr-12 xs:mt-6 xs:bolck xl:hidden">
              <button
                onClick={movePrev}
                className=" rounded-[100%] bg-gradient-to-b from-Tosca to-Contessa
            hover:bg-gradient-to-b hover:from-contessa hover:to-tosca 
             text-white w-[40px] h-[40px] flex items-center justify-center  z-10 p-0 m-0 transition-all ease-in-out duration-300"
              >
                <ArrowSmallLeftIcon className=" text-white font-bold w-6 h-5 hover:scale-150 " />
              </button>
              <button
                onClick={moveNext}
                className=" rounded-[100%] ml-4 bg-gradient-to-b from-Tosca to-Contessa 
            hover:bg-gradient-to-b hover:from-contessa hover:to-tosca 
            text-white w-[40px] h-[40px] flex items-center justify-center  z-10 p-0 m-0 transition-all ease-in-out duration-300 mr-4"
              >
                <ArrowSmallRightIcon className="font-bold text-white w-6 h-5 hover:scale-150 " />
                {/* <span className="sr-only">Next</span> */}
              </button>
            </div>
          </>
        ) : (
          <p className="text-center"></p>
        )}
      </div>
    </section>
  );
};

export default Slider;
