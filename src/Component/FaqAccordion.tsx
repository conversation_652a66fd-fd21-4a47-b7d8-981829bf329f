// FaqAccordion.js

import React, { useState } from "react";
import { motion, useTransform, useViewportScroll } from "framer-motion";

interface FaqAccordionProps {
  question: any;
  answer: any;
  defaultOpen?: boolean;
}

const FaqAccordion: React.FC<FaqAccordionProps> = ({ question, answer, defaultOpen }) => {
  const [isOpen, setIsOpen] = useState(defaultOpen);

  const toggleAccordion = () => {
    setIsOpen(!isOpen);
  };
  const { scrollYProgress } = useViewportScroll();
  const scale = useTransform(scrollYProgress, [0, 0.9], [0.5, 1]);
  return (
    <div
      className="select-none flex justify-center items-center"
      onClick={toggleAccordion}
    >
      <motion.div
        // style ={{ scale }}

        className="border- bg-gradient-to-b from-Contessa  to-Tosca p-[2px] rounded-[20px] mb-4"
      >
        <div className="inset-0 bg-[linear-gradient(to_top,_#241415,_black)]  md:w-[520px] xs:w-[350px] p-[10px]- rounded-[20px] p-[20px]">
          <div className="flex justify-between items-center cursor-pointer transition duration-300 ease-in-out">
            <h1 className="font-outfit font-semibold xs:text-[14px] lg:text-[18px] text-white">
              {question}
            </h1>
            <span
              className={`text-2xl transition duration-300 ease-in-out ${
                isOpen ? "rotate-180" : ""
              }`}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth="1.5"
                stroke="currentColor"
                className="w-6 h-6"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M19.5 8.25l-7.5 7.5-7.5-7.5"
                />
              </svg>
            </span>
          </div>
          <div
            className={`overflow-hidden transition-max-height duration-300 ease-in-out ${
              isOpen ? "max-h-[250px]" : "max-h-0"
            }`}
          >
            <p className="mt-2 font-outfit text-sm px-4- pt-0 py-0 pb-[12px]">
              {answer}
            </p>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default FaqAccordion;
