"use client";

import { motion, useScroll } from "framer-motion";

import Image from "next/image";
import { Images } from "@/constant";
import { LabelComponent } from "./label";
import NumberCounter from "./Animatedd";
import { Strings } from "@/constant";

const CountComponent = () => {
  
  return (
    <div
      className=" bg-red-400 relative  xs:bg-gradient-to-r from-Cod_Gray to-Mine_Shaft   md:flex rounded-3xl
        xl:w-[78%] 2xl:w-[75%]  lg:w-[82%]  md:w-[94%] sm:w-[96%]  xs:w-[50%] xs:px-2 xs:h-[400px] xs:text-center xs:items-center mx-auto z-20  xl:h-[288px] lg:h-[230px] sm:h-[200px] justify-around items-center md:px-4- xs:block xs:mt-[-20px]"
    >
      <div className="relative xs:h-[290px] sm:h-[200px] md:h-[290px] xs:w-full lg:w-[1200px] flex">
        <Image
          src={Images.line}
          alt="/"
          height={90}
          width={1200}
          className="xs:hidden sm:block"
        />
        <div className=" absolute top-0 left-0  w-full h-full flex items-center justify-between xs:flex-col sm:flex-row ">
          <div className=" w-full h-full sm:flex sm:justify-center sm:items-center xl:pr-3 md:space-x-2 xs:py-4  ">
            <div className=" xs:flex xs:justify-center xs:items-center sm:block ">
              <div className=" flex items-center justify-center text-white ">
                <NumberCounter end={30} />
              </div>
              <Image
                src={Images.pluss}
                alt="animated logo"
                width={15}
                height={15}
                className="text-Contessa  ml-2 mt-2 sm:hidden xs:block"
              />
            </div>
            <div className="xs:-mt-[0.5rem]  xl:space-y-4    sm:space-y-2">
              <Image
                src={Images.pluss}
                alt="animated logo"
                width={20}
                height={20}
                className="text-Contessa md:w-[25px] md:h-[25px] sm:w-[20px] sm:h-[20px] sm:block xs:hidden sm:my-3"
              />
              <div className="">
                <LabelComponent
                  className="font-outfit text-[#ffffff80] font-normal  lg:text-[22px] md:text-sm xs:text-[16px] sm:text-[14px]"
                  label={Strings.Developer}
                />
              </div>
            </div>
          </div>
          <div className=" w-full h-full sm:flex sm:justify-center sm:items-center xl:pl-[30px] xl:pr-[20px] md:space-x-2 md:py-4 xs:py-4">
            <div className="text-white   xs:flex xs:justify-center xs:items-center md:block">
              <NumberCounter end={25} />
              <Image
                src={Images.pluss}
                alt="animated logo"
                width={15}
                height={15}
                className="text-Contessa ml-2 mt-2  sm:hidden xs:block "
              />
            </div>
            <div className="  xs:-mt-[0.5rem] xl:space-y-4   md:space-y-2   ">
              <Image
                src={Images.pluss}
                alt="animated logo"
                width={20}
                height={20}
                className="text-Contessa md:w-[25px] md:h-[25px] sm:w-[20px] sm:h-[20px] sm:block xs:hidden sm:my-2"
              />
              <div className="">
                <LabelComponent
                  className="font-outfit leading-normal text-[#ffffff80] font-normal  lg:text-[22px] md:text-sm xs:text-[16px] sm:text-[14px]"
                  label={Strings.UI_UX}
                />
              </div>
            </div>
          </div>
          <div className=" w-full h-full sm:flex sm:justify-center sm:items-center  xl:px-3- md:space-x-2 md:py-4 xs:py-4">
            <div className="xs:flex xs:justify-center xs:items-center md:block text-white">
              <NumberCounter end={20} />
              <Image
                src={Images.pluss}
                alt="animated logo"
                width={15}
                height={15}
                className="text-Contessa ml-2 mt-2 sm:hidden xs:block"
              />
            </div>
            <div className="xs:-mt-[0.5rem]  xl:space-y-4   md:space-y-2">
              <Image
                src={Images.pluss}
                alt="animated logo"
                width={20}
                height={20}
                className="text-Contessa md:w-[25px] md:h-[25px] sm:w-[20px] sm:h-[20px] sm:block xs:hidden sm:my-2"
              />
              <div className="">
                <LabelComponent
                  className="font-outfit text-[#ffffff80] lg:text-[22px]  md:text-sm  xs:text-[16px] md:text-[16px] sm:text-[14px] font-normal  "
                  label={Strings.Project_M}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CountComponent;
