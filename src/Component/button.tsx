"use client";

import { Button } from "antd";
import React from "react";

interface ButtonProps {
  className: string;
  children?: React.ReactNode;
  onPress?: () => void;
}
export const ButtonComponent: React.FC<ButtonProps> = ({
  children,
  className,
  onPress,
  ...props
}) => {
  return (
    <>
      <div>
        <Button className={className} onClick={onPress} {...props}>
          {children}
        </Button>
      </div>
    </>
  );
};
