import { Images, Strings } from "../constant";

import Image from "next/image";
import { LabelComponent } from "./label";
import React from "react";

interface OptionmodalProps {
  isImageShow?: boolean;
  buttonText: string;
  label: string;
  imageSrc: any;
  isSelected: boolean;
  onClick: () => void;
}

const Optionmodal: React.FC<OptionmodalProps> = ({
  isImageShow = true,
  buttonText,
  label,
  imageSrc,
  isSelected,
  onClick,
}) => {
  return (
    <div>
      <button
        className={`relative border-tosca border text-white h-12 flex justify-center items-center px-2 rounded-lg inset-0 bg-[linear-gradient(to_top,_#241415,_black)] ${
          isSelected ? "border-opacity-100" : "border-opacity-70"
        }`}
        onClick={() => {
          onClick();
          // console.log(`Selected option: ${label}`);
        }}
      >
        <div className={`flex justify-center items-center`}>
          <div>
            {isImageShow && (
              <Image
                src={imageSrc}
                alt="/"
                height={500}
                width={500}
                className="rounded-full h-[30px] w-[30px] mr-2"
              />
            )}
          </div>
          <h1 className={`font-light xs:text-sm lg:text-lg text-white`}>
            {label}
          </h1>
        </div>
        {isSelected && (
          <Image
            src={Images.Checkicon}
            alt="/"
            height={500}
            width={500}
            className="h-5 w-5 absolute -top-1 -right-1"
          />
        )}
      </button>
    </div>
  );
};

export default Optionmodal;
