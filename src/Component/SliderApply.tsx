import { useEffect, useRef, useState } from "react";

import Image from "next/image";
import { Images } from "@/constant";
import React from "react";
import Slider from "react-slick";
import { Strings } from "@/constant";
import axios from "axios";

interface SuccessStory {
  successStory: string;
  profilePicture: string;
  firstName: string;
  designation: string;
}

interface SliderApplyProps {
  successStoryDetailsData: SuccessStory[];
}

const SliderApply: React.FC<SliderApplyProps> = ({
  successStoryDetailsData,
}) => {
  const sliderRef = useRef<Slider>(null);

  const settings: any = {
    dots: true,
    infinite: true,
    speed: 1000,
    nextArrow: null,
    prevArrow: null,
    arrows: false,
    responsive: [
      {
        breakpoint: 2500,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 1,
        },
      },
      {
        breakpoint: 900,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
        },
      },
    ],
  };

  const [activeIndex, setActiveIndex] = useState(0);

  const handleSlideChange = (index: number) => {
    setActiveIndex(index);
  };

  return (
    <div>
      {successStoryDetailsData.length > 0 ? (
        <div className="flex flex-col sm:justify-center sm:items-center bg-red-900- ">
          <h2 className="text-center font-outfit font-bold text-[40px] mb-[50px] text-white">
            {Strings.SUCCESS}
          </h2>
          <div className="slick_custom h-[500px] xl:w-[1450px] xs:mx-[20px] md:mx-0">
            <Slider
              ref={sliderRef}
              {...settings}
              afterChange={handleSlideChange}
            >
              {successStoryDetailsData.map((ele, index) => (
                <div
                  key={index}
                  className="lg:h-[450px] xs:max-h-[650px] wi box-border bg-[#1B1B1B] rounded-3xl border border-contessa transition-width duration-1000 ease-in-out  overflow-hidden gap-[20px] flex justify-center items-center xs:py-[20px] lg:py-0 "
                >
                  <div
                    className={`wi- box-border flex justify-center items-center lg:flex-row xs:flex-col-reverse h-[500px]- rounded-3xl   transition-width- duration-1000 ease-in-out overflow-hidden xl:my-[55px]  lg:mt-[70px] lg:mb-[50px]   `}
                  >
                    <div
                      className={`box-border xs:text-justify md:text-left text-content-hide font-outfit font-extralight xl:text-[17px] lg:text-[14px] xs:text-[15px] md:text-[17px] lg:ml-5 xl:ml-[28px] flex flex-col  xs:px-[20px]  md:px-[10px] lg:px-0 lg:py-6 text-[#A3A2A2] `}
                    >
                      {ele.successStory}
                    </div>
                    <div>
                      <div className="flex  items-center flex-col xl:w-[350px] lg:w-[280px] xs:w-[280px] ">
                        <div className="relative mb-[20px] ">
                          <Image
                            width={700}
                            height={700}
                            src={ele.profilePicture}
                            alt="image_men"
                            className=" rounded-full w-[230px] h-[230px] object-cover"
                          />

                          <Image
                            src={Images.Cardbg}
                            alt="/"
                            height={325}
                            width={325}
                            className="absolute lg:left-0 lg:bottom-[-134px]"
                          />
                        </div>
                        <div className="">
                          <h2 className="text-4xl font-outfit font-bold not-italic text-white  ">
                            {ele.firstName}
                          </h2>
                        </div>
                        <div className=" text-center  w-full">
                          <p className="w-full text-2xl my-[10px] font-light font-outfit not-italic text-[#A3A2A2]">
                            {ele.designation}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </Slider>
          </div>
        </div>
      ) : (
        <p className="text-center"></p>
      )}
    </div>
  );
};

export default SliderApply;
