import { Strings } from "@/constant";
import { executeCode } from "../../../api";
import { useState } from "react";

type OutputProps = {
  editorRef: React.RefObject<any>;
  language: string;
  handleContinue: () => void;
  theme: string;
};

const Output: React.FC<OutputProps> = ({
  editorRef,
  language,
  handleContinue,
  theme = "vs-dark",
}) => {
  const [output, setOutput] = useState<string[] | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isError, setIsError] = useState(false);

  const runCode = async () => {
    const sourceCode = editorRef.current?.getValue();
    if (!sourceCode) return;
    try {
      setIsLoading(true);
      const { run: result } = await executeCode(language, sourceCode);
      setOutput(result.output.split("\n"));
      setIsError(!!result.stderr);
    } catch (error) {
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div>
      <div
        className={`p-2 space-y-1 border-y  ${
          theme === "vs-light"
            ? "bg-white text-black border-[#EEEEEE]"
            : "bg-[#1E1E1E] text-white border-[#282828]"
        }`}
      >
        <h1 className="font-medium text-sm">Output</h1>
      </div>
      <div
        className={` ${
          theme === "vs-light"
            ? "bg-white text-black"
            : "bg-[#1E1E1E] text-white"
        }  px-2 pb-2  font-normal text-sm min-h-32 max-h-32 w-full overflow-y-scroll`}
      >
        {output ? (
          output.map((line, i) => (
            <p key={i} className="font-normal text-sm">
              {line}
            </p>
          ))
        ) : (
          <p className="text-sm">{Strings.RUN}</p>
        )}
      </div>
      <div className="mt-4 flex items-center justify-between text-sm font-normal">
        <h1>{Strings.Note}</h1>
        <div className="flex gap-x-2 items-center">
          <button
            className="bg-[linear-gradient(to_top,_#241415,_black)] border border-[#8d3f42]/50 py-2 px-5 text-white rounded-full"
            disabled={isLoading}
            onClick={runCode}
          >
            {isLoading ? "Running..." : "Run"}
          </button>
          <button
            className="bg-gradient-to-r from-[#481e1f] to-[#6b443a] py-2 px-4 text-white rounded-full"
            onClick={handleContinue}
          >
            Continue
          </button>
        </div>
      </div>
    </div>
  );
};

export default Output;
