import { DownOutlined } from "@ant-design/icons";
import { LANGUAGE_VERSIONS } from "@/constant";

type LanguageSelectorProps = {
  language: string;
  onSelect: (lang: string) => void;
};

const languages = Object.entries(LANGUAGE_VERSIONS);

const LanguageSelector: React.FC<LanguageSelectorProps> = ({
  language,
  onSelect,
}) => {
  return (
    <div className="relative w-[200px]">
      <select
        id="language-select"
        value={language}
        onChange={(e) => onSelect(e.target.value)}
        className="text-white text-sm pr-7 p-2 bg-[linear-gradient(to_top,_#241415,_black)] outline-none border border-tosca border-opacity-50 rounded-md h-12 w-full appearance-none"
      >
        <option value="" disabled>
          Select language
        </option>
        {languages.map(([lang, version]) => (
          <option key={lang} value={lang} className="text-gray-900">
            {lang} ({version})
          </option>
        ))}
      </select>
      <div className="absolute inset-y-0 right-0 flex items-center px-4 pointer-events-none">
        <DownOutlined className="h-3 w-3 text-white" />
      </div>
    </div>
  );
};

export default LanguageSelector;
