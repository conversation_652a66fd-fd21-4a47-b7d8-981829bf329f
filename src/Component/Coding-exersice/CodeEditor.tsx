import "../../app/globals.css";

import { CODE_SNIPPETS, Language } from "@/constant";
import { ClockCircleOutlined, DownOutlined } from "@ant-design/icons";
import { RefObject, SetStateAction, useRef, useState } from "react";

import { Editor } from "@monaco-editor/react";
import LanguageSelector from "./LanguageSelector";
import { Strings } from "@/constant";
import { executeCode } from "../../../api";
import { useEffect } from "react";

interface CompilerProps {
  question: string;
  minutes: number;
  seconds: number;
  handleContinue: () => void;
  videoRef: RefObject<HTMLVideoElement>;
  onCodeSubmit: (code: string) => void;
}

const CodeEditor: React.FC<CompilerProps> = ({
  question,
  minutes,
  seconds,
  handleContinue,
  videoRef,
  onCodeSubmit,
}) => {
  const editorRef = useRef<any>(null);
  const [value, setValue] = useState<string>("");
  const [language, setLanguage] = useState<Language>("javascript");
  const [theme, setTheme] = useState("");
  const [output, setOutput] = useState<string[] | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isError, setIsError] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleContinueClick = () => {
    if (editorRef.current) {
      const code = editorRef.current.getValue();
      onCodeSubmit(code);
    }
    handleContinue();
  };

  const openModal = () => {
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };

  const runCode = async () => {
    const sourceCode = editorRef.current?.getValue();
    if (!sourceCode) return;
    try {
      setIsLoading(true);
      const { run: result } = await executeCode(language, sourceCode);
      setOutput(result.output.split("\n"));
      setIsError(!!result.stderr);
    } catch (error) {
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  const onMount = (editor: any) => {
    editorRef.current = editor;
    editor.focus();
  };

  const onSelect = (selectedLanguage: string) => {
    setLanguage(selectedLanguage as Language);
    setValue(CODE_SNIPPETS[selectedLanguage as Language]);
  };

  const handleThemeChange = (event: {
    target: { value: SetStateAction<string> };
  }) => {
    setTheme(event.target.value);
  };

  useEffect(() => {
    if (videoRef.current) {
      console.log("✅ videoRef found!", videoRef.current);

      setTimeout(() => {
        if (videoRef.current && !videoRef.current.srcObject) {
          console.warn("❌ No video feed yet! Retrying...");

          navigator.mediaDevices
            .getUserMedia({ video: true, audio: true })
            .then((stream) => {
              console.log("🎥 Retrying: Setting video stream...");
              videoRef.current!.srcObject = stream;
            })
            .catch((error) => {
              console.error("🚨 Error accessing camera:", error);
            });
        } else {
          console.log("🎥 Video feed already exists.");
        }
      }, 500);
    }
  }, [videoRef.current]);

  const wordCount = question ? question.split(/\s+/).length : 0;

  return (
    <div className="flex w-full h-full bg-green-300-">
      {/* Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-[#1E1E1E] rounded-lg p-6 max-w-md w-full mx-4">
            <h2 className="text-xl font-semibold text-white mb-4">Submit Code?</h2>
            <p className="text-gray-300 mb-6">
              Are you sure you want to submit your code? This action cannot be undone.
            </p>
            <div className="flex justify-end gap-4">
              <button
                onClick={closeModal}
                className="px-4 py-2 bg-gray-600 text-white rounded-full hover:bg-gray-700 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  closeModal();
                  handleContinueClick();
                }}
                className="px-4 py-2 bg-gradient-to-r from-[#481e1f] to-[#6b443a] text-white rounded-full hover:opacity-90 transition-opacity"
              >
                Yes, Continue
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="bg-white p-4 h-[100vh] w-[40%] text-black justify-center flex flex-col">
        <div className="bg-tosca bg-opacity-15 p-2 rounded-md">
          <h1 className="font-semibold text-black text-xl text-start">
            Coding exersice
          </h1>
          <h2
            className={`font-normal text-black text-base text-start p-2 ${wordCount > 50 ? "max-h-[300px] overflow-y-auto" : ""
              } pre-line`}
            dangerouslySetInnerHTML={{
              __html: question
                ? question
                  .replace(/\n/g, "<br>")
                  .replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>")
                  .replace(/^\* /gm, "")
                  .replace(/^- /gm, "")
                : "",
            }}
          ></h2>

        </div>
        <div className="flex-1"></div>
        <div className="mb-12 mx-auto h-48 w-[290px] xl:w-[320px] rounded-md bg-gradient-to-r from-[#481e1f] to-[#6b443a] p-[2px]">
          <div className="h-full w-full rounded-md bg-black">
            <video
              ref={videoRef}
              autoPlay
              playsInline
              muted
              className="w-full h-full rounded-md object-cover"
              style={{ transform: 'scaleX(-1)'}}
            />
          </div>
        </div>
      </div>
      <div className="p-4 w-full h-full bg-red-300- flex flex-col">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-x-4">
            <LanguageSelector language={language} onSelect={onSelect} />
            <div className="relative w-[200px]">
              <label htmlFor="theme-select" className="sr-only">
                Select Theme
              </label>
              <select
                id="theme-select"
                value={theme}
                onChange={handleThemeChange}
                className="text-white text-sm pr-7 p-2 bg-[linear-gradient(to_top,_#241415,_black)] outline-none border border-tosca border-opacity-50 rounded-md h-12 w-full appearance-none"
              >
                <option value="" disabled>
                  Select theme
                </option>
                <option className="text-black hover:bg-tosca" value="vs-dark">
                  Dark
                </option>
                <option className="text-black hover:bg-tosca" value="vs-light">
                  Light
                </option>
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center px-4 pointer-events-none">
                <DownOutlined className="h-3 w-3 text-white" />
              </div>
            </div>
          </div>
          <div className="bg-gradient-to-l from-[#481e1f] to-[#6b443a] rounded-md w-[100px] p-[1px]">
            <div className=" flex items-center justify-center gap-x-2 rounded-md bg-[linear-gradient(to_top,_#241415,_black)] p-2 text-xl font-bold text-white">
              <ClockCircleOutlined className=" text-[#8d3f42]" />
              <p>{`${minutes}:${seconds < 10 ? "0" : ""}${seconds}`}</p>
            </div>
          </div>
        </div>
        <Editor
          // options={{
          //   minimap: { enabled: false },
          // }}
          width="100%"
          height="100%"
          language={language || "javascript"}
          theme={theme || "vs-dark"}
          defaultValue={CODE_SNIPPETS[language]}
          onMount={onMount}
          value={value}
          onChange={(val) => {
            setValue(val || "");
          }}
        />
        <div>
          <div
            className={`p-2 space-y-1 border-y  ${theme === "vs-light"
              ? "bg-white text-black border-[#EEEEEE]"
              : "bg-[#1E1E1E] text-white border-[#282828]"
              }`}
          >
            <h1 className="font-medium text-sm">Output</h1>
          </div>
          <div
            className={` ${theme === "vs-light"
              ? "bg-white text-black"
              : "bg-[#1E1E1E] text-white"
              }  px-2 pb-2  font-normal text-sm min-h-32 max-h-32 w-full overflow-y-scroll`}
          >
            {output ? (
              output.map((line, i) => (
                <p key={i} className="font-normal text-sm">
                  {line}
                </p>
              ))
            ) : (
              <p className="text-sm">{Strings.RUN}</p>
            )}
          </div>
          <div className="mt-4 flex items-center justify-between text-sm font-normal">
            <h1>{Strings.Note}</h1>
            <div className="flex gap-x-2 items-center">
              <button
                className="bg-[linear-gradient(to_top,_#241415,_black)] border border-[#8d3f42]/50 py-2 px-5 text-white rounded-full"
                disabled={isLoading}
                onClick={runCode}
              >
                {isLoading ? "Running..." : "Run"}
              </button>
              <button
                className="bg-gradient-to-r from-[#481e1f] to-[#6b443a] py-2 px-4 text-white rounded-full"
                onClick={openModal}
              >
                Continue
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CodeEditor;