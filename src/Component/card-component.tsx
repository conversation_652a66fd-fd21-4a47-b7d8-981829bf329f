import Image from "next/image";
import { LabelComponent } from "./label";
import React from "react";

interface CardComponentProps {
  imageSrc: any;
  name?: string;
  title?: string;
  socialLinks?: {
    facebook?: string;
    instagram?: string;
  };
}

export const CardComponent: React.FC<CardComponentProps> = ({
  imageSrc,
  name,
  title,
  socialLinks,
}) => {
  return (
    <div className="flex justify-center">
      <div className="border relative rounded-2xl xs:h-[330px] md:h-[320px] lg:h-[335px] xs:w-[280px] sm:w-[320px]- md:w-[280px] lg:w-[280px]- xl:w-[290px] xlg:w-[320px] mb-6 group hover:flex">
        <div
          className="absolute -inset-px bg-gradient-to-t from-tosca to-contessa rounded-2xl "
          aria-hidden="true"
        ></div>
        <div
          className="absolute inset-0 bg-[radial-gradient(ellipse_at_top_right,_var(--tw-gradient-stops))] from-[#241415] via-black to-black rounded-2xl xs:pt-8 lg:pt-[3rem] hover:pt-1- duration-500 overflow-hidden"
          aria-hidden="true"
        >
          <div className="group">
            <div className="flex justify-center ">
              <Image
                src={imageSrc}
                alt="/"
                height={700}
                width={700}
                className="rounded-full h-[150px] w-[150px] object-cover"
              />
            </div>
            <LabelComponent
              label={name}
              className="text-white font-outfit font-semibold xs:text-2xl lg:text-3xl justify-center flex mt-7 "
            />
            <LabelComponent
              label={title}
              className=" font-outfit font-medium xs:text-lg lg:text-xl text-[#A3A2A2] flex justify-center mt-3"
            />
            {/* <div className="xs:mx-14 md:mx-10 lg:mx-16 xl:mx-20 justify-between hidden group-hover:flex xs:mt-3 md:mt-2 lg:mt-[14px] transition-y duration-5000">
              <a
                href={socialLinks?.facebook}
                target="_blank"
                rel="noopener noreferrer"
              >
                <div className="rounded-full opacity-70 hover:opacity-100 bg-gradient-to-t from-tosca to-contessa h-12 w-12 flex justify-center items-center cursor-pointer">
                  <Image
                    src={Images.Facebook}
                    preview={false}
                    alt="/"
                    height={30}
                    width={35}
                  />
                </div>
              </a>
              <a
                href={socialLinks?.instagram}
                target="_blank"
                rel="noopener noreferrer"
              >
                <div className="rounded-full opacity-70 hover:opacity-100 bg-gradient-to-t from-tosca to-contessa h-12 w-12 flex justify-center items-center cursor-pointer ">
                  <Image
                    src={Images.Instaa}
                    preview={false}
                    alt="/"
                    height={27}
                    width={27}
                  />
                </div>
              </a>
            </div> */}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CardComponent;
