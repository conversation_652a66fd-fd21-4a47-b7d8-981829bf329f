import Applyasanengineer, { toggleModal1 } from "./Applyasanengineer";
import Hiretopengineer, { toggleModal } from "./Hiretopengineer";
import React, { useEffect, useRef, useState } from "react";

import { ArrowLeftIcon } from "@heroicons/react/24/outline";
import Image from "next/image";
import { Images } from "@/constant";
import { LabelComponent } from "./label";
import Link from "next/link";
import { Strings } from "@/constant";
import { XMarkIcon } from "@heroicons/react/24/outline";
import { useRouter } from "next/router";

export default function NavbarComponent() {
  const menuRef = useRef<HTMLDivElement>(null);
  const [showMenu, setShowMenu] = useState(false);
  const [showProducts, setShowProducts] = useState(false);
  const [animating, setAnimating] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [isOpen1, setIsOpen1] = useState(false);
  const router = useRouter();

  const toggleMenu = () => {
    if (showMenu) {
      setAnimating(true);
    } else {
      setShowMenu(true);
    }
  };

  const toggleProducts = () => {
    if (showProducts) {
      setAnimating(true);
    } else {
      setShowProducts(true);
    }
  };

  useEffect(() => {
    if (router.query.hireengineer) {
      setIsOpen(true);
    }
  }, [router.query.hireengineer]);

  const handleButtonClick = () => {
    toggleModal(isOpen, setIsOpen);
  };

  const handleButtonClick1 = () => {
    if (router.pathname === "/apply-as-an-engineer") {
      toggleModal1(isOpen1, setIsOpen1);
    } else {
      router.push("/apply-as-an-engineer");
    }
  };

  useEffect(() => {
    if (animating) {
      const timer = setTimeout(() => {
        setShowMenu(false);
        setAnimating(false);
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [showMenu, animating]);

  useEffect(() => {
    if (showMenu || showProducts) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "";
    }
  }, [showMenu, showProducts]);

  const productsData = [
    {
      image: "/images/Ai.png",
      title: "AI-based Resume Scanner",
      href: "/ats",
      description:
        "An AI-powered resume scanner that analyzes your strengths, weaknesses, and provides a percentage score to help you stand",
    },
    {
      image: "/images/gpt_vetting",
      title: "GPT-vetting",
      href: "/gpt-vetting",
      description:
        "A GPT-4 powered test deeply pre-screens the technical knowledge of all candidates",
    },
  ];

  return (
    <>
      <div className="flex w-full items-center justify-between py-4 xl:py-10 px-4 xl:px-7">
        <div className="flex items-center gap-x-2 xl:gap-x-6 xs:hidden lg:flex">
          <Link href={"/"}>
            <Image
              src={Images.logo}
              width={500}
              height={500}
              alt="/"
              className="w-[165px] h-[35px] -ml-4"
            />
          </Link>
          <div
            className="relative -ml-4"
            onMouseLeave={() => setIsDropdownOpen(false)}
          >
            <div
              onMouseEnter={() => setIsDropdownOpen(true)}
              className="text-white cursor-pointer text-base xl:text-lg hover:opacity-[0.6] flex items-center xl:gap-x-1"
            >
              Products
              <Image
                src={"/images/DownIcon.svg"}
                alt="/"
                width={500}
                height={500}
                className={`h-4 w-4 ${
                  isDropdownOpen ? "rotate-180 duration-300" : "duration-300"
                }`}
              />
            </div>
            {isDropdownOpen && (
              <div
                onMouseLeave={() => setIsDropdownOpen(false)}
                className="absolute top-6 xl:top-7 -left-2 bg-black border-2 space-y-3 border-[#241415] text-white w-[480px] rounded-lg p-3"
              >
                {productsData.map((item, index) => (
                  <div key={index}>
                    <Link href={item.href}>
                      <div className="flex cursor-pointer items-center gap-x-3 rounded-lg p-2 bg-black border-opacity-50 hover:border-opacity-100 border border-tosca">
                        <div className="bg-[linear-gradient(to_top,_#241415,_black)] border border-[#241415] flex justify-center items-center rounded-lg h-auto w-auto p-2 shrink-0">
                          <Image
                            src={item.image}
                            alt="/"
                            width={500}
                            height={500}
                            className=" h-16 w-16"
                          />
                        </div>
                        <div className="text-start">
                          <h1 className="font-medium text-lg text-white">
                            {item.title}
                          </h1>
                          <p className="text-sm text-[#9597A3] font-normal">
                            {item.description}
                          </p>
                        </div>
                      </div>
                    </Link>
                  </div>
                ))}
              </div>
            )}
          </div>
          <div className="text-white cursor-pointer text-base xl:text-lg hover:opacity-[0.6]">
            <Link href="/how-it-works">{Strings.How_it_works}</Link>
          </div>
          <div className="text-white cursor-pointer text-base xl:text-lg hover:opacity-[0.6]">
            <Link href="/vetting-process">{Strings.VETTING_PROCESS}</Link>
          </div>
          <div className="text-white cursor-pointer text-base xl:text-lg hover:opacity-[0.6]">
            <Link href="/about-us">{Strings.ABOUT_US}</Link>
          </div>
        </div>
        <div className="flex gap-x-2 xl:gap-x-6 items-center xs:hidden lg:flex">
          <div
            onClick={handleButtonClick1}
            className="cursor-pointer text-base xl:text-lg text-white hover:opacity-[0.6]"
          >
            <Link href="/apply-as-an-engineer">
              {Strings.APPLY_AS_AN_ENGINEER}
            </Link>
          </div>
          <button
            onClick={handleButtonClick}
            className="rounded-full flex justify-center text-base xl:text-lg items-center  px-4 py-2 xl:px-6 outline-none bg-gradient-to-t from-Contessa to-Tosca hover:bg-gradient-to-b hover:from-Contessa hover:to-Tosca transition-transform hover:scale-105 transform duration-300  text-white "
          >
            <LabelComponent label={Strings.Hire_a_Remote_job} />
          </button>
          <Link
            target="_blank"
            href={process.env.NEXT_PUBLIC_CLIENT_URL || "#"}
          >
            <button className="rounded-full text-base xl:text-lg flex justify-center  items-center text-white bg-gradient-to-l from-Contessa to-Tosca hover:bg-gradient-to-t hover:from-black hover:to-black p-[2px] transition-transform hover:scale-105 transform duration-300">
              <div className="rounded-full flex  px-4 py-2 xl:px-6 bg-[linear-gradient(to_top,_#241415,_black)] hover:bg-gradient-to-t from-Contessa to-Tosca justify-center items-center h-full w-full">
                {Strings.Log_In}
              </div>
            </button>
          </Link>
        </div>
        <div className="flex justify-between w-full xs:flex lg:hidden">
          <Link href={"/"}>
            <Image
              src={Images.logo}
              width={500}
              height={500}
              alt="/"
              className="w-[165px] h-[35px] -ml-4"
            />
          </Link>
          <button onClick={toggleMenu}>
            <Image
              src={Images.menu}
              width={500}
              height={500}
              alt="/"
              className="h-[35px] w-[35px]"
            />
          </button>
        </div>
      </div>
      {showMenu && (
        <div
          ref={menuRef}
          className={`fixed flex justify-center items-center top-0 left-0 right-0 bottom-0 h-full w-full bg-black z-40 ${
            animating ? "menu-slide-up" : "menu-slide-down"
          }`}
        >
          <Link href={"/"}>
            <Image
              src={Images.logo}
              width={500}
              height={500}
              alt="/"
              className="w-[165px] h-[35px] -ml-4 fixed top-7 left-4"
            />
          </Link>
          <button onClick={toggleMenu}>
            <XMarkIcon className="h-7 w-7 text-white fixed top-7 right-4" />
          </button>
          <div className="flex flex-col items-center gap-y-5">
            <div
              onClick={toggleProducts}
              className="text-white  cursor-pointer text-lg hover:opacity-[0.6] flex items-center xl:gap-x-1"
            >
              Products
              <Image
                src={"/images/DownIcon.svg"}
                alt="/"
                width={500}
                height={500}
                className={`h-4 w-4 ${
                  isDropdownOpen ? "rotate-180 duration-300" : "duration-300"
                }`}
              />
            </div>
            <div className="text-white cursor-pointer text-lg hover:opacity-[0.6]">
              <Link href="/how-it-works">{Strings.How_it_works}</Link>
            </div>
            <div className="text-white cursor-pointer text-lg hover:opacity-[0.6]">
              <Link href="/vetting-process">{Strings.VETTING_PROCESS}</Link>
            </div>
            <div className="text-white cursor-pointer text-lg hover:opacity-[0.6]">
              <Link href="/about-us">{Strings.ABOUT_US}</Link>
            </div>
            <div
              onClick={handleButtonClick1}
              className="cursor-pointer text-lg text-white hover:opacity-[0.6]"
            >
              <Link href="/apply-as-an-engineer">
                {Strings.APPLY_AS_AN_ENGINEER}
              </Link>
            </div>
            <div className="fixed bottom-0 w-full p-3">
              <button
                onClick={handleButtonClick}
                className="w-full rounded-lg flex justify-center text-lg items-center py-[6px] text-center outline-none bg-gradient-to-t from-Contessa to-Tosca hover:bg-gradient-to-t transition-transform hover:scale-100 transform duration-700 text-white "
              >
                <LabelComponent label={Strings.Hire_a_Remote_job} />
              </button>
              <Link
                target="_blank"
                href={process.env.NEXT_PUBLIC_CLIENT_URL || "#"}
              >
                <button className="mt-2 w-full rounded-lg text-lg flex justify-center  items-center text-white bg-gradient-to-t from-Contessa to-Tosca p-[1px] lg:p-[2px] transition-transform hover:scale-100 transform duration-700">
                  <div className="bg-black- rounded-lg flex py-[6px] text-center bg-[linear-gradient(to_top,_#241415,_black)] justify-center items-center h-full w-full">
                    {Strings.Log_In}
                  </div>
                </button>
              </Link>
            </div>
          </div>
        </div>
      )}
      {showProducts && (
        <div className="fixed inset-0 bg-black z-40 flex flex-col">
          <button onClick={() => setShowProducts(false)}>
            <ArrowLeftIcon className="w-7 h-7 fixed top-7 left-4 text-white" />
          </button>
          <button
            onClick={() => {
              setShowProducts(false);
              setShowMenu(false);
            }}
          >
            <XMarkIcon className="h-7 w-7 text-white fixed top-7 right-4" />
          </button>
          <div className="flex-grow overflow-y-auto no-scrollbar p-5 mt-20">
            <h1 className="font-medium text-xl text-white">Products</h1>
            <div className="mt-2 bg-black border-2 space-y-4 border-[#351e1f] text-white w-full rounded-lg p-2 md:p-3">
              {productsData.map((item, index) => (
                <div key={index}>
                  <Link href={item.href}>
                    <div className="cursor-pointer flex items-center gap-x-4 rounded-lg p-2 bg-black border-opacity-50 hover:border-opacity-100 border border-tosca">
                      <div className="bg-[linear-gradient(to_top,_#241415,_black)] border border-[#241415] flex justify-center items-center rounded-lg h-20 w-20 shrink-0">
                        <Image
                          src={item.image}
                          alt="/"
                          width={500}
                          height={500}
                          className="h-16 w-16"
                        />
                      </div>
                      <div className="text-start">
                        <h1 className="font-medium text-[15px] text-white">
                          {item.title}
                        </h1>
                        <p className="text-xs text-[#9597A3] font-normal">
                          {item.description}
                        </p>
                      </div>
                    </div>
                  </Link>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
      <Hiretopengineer isOpen={isOpen} setIsOpen={setIsOpen} />
      <Applyasanengineer isOpen1={isOpen1} setIsOpen1={setIsOpen1} />
    </>
  );
}
