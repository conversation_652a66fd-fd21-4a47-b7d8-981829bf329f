// import "react-orbits/dist/index.css";

import { Images, Strings } from "@/constant";
import { Orbit, Planet, ReactOrbits } from "react-orbits";
import { motion, useTransform, useViewportScroll } from "framer-motion";
import { useEffect, useState } from "react";

import Image from "next/image";
import { LabelComponent } from "./label";
import { useRef } from "react";

const CircleRounded = () => {
  const containerRef = useRef(null);

  const { scrollYProgress } = useViewportScroll();
  const scale = useTransform(scrollYProgress, [0, 0.9], [0.5, 1]);

  const [marginBetweenOrbits, setMarginBetweenOrbits] = useState(60);
  const [firstOrbitDiameter, setFirstOrbitDiameter] = useState(160);
  const handleWindowSizeChange = () => {
    const screenWidth = window.innerWidth;

    if (screenWidth >= 1200) {
      setFirstOrbitDiameter(140);
      // console.log("big");
    } else if (screenWidth >= 1024) {
      setFirstOrbitDiameter(140);
    } else if (screenWidth >= 768) {
      setFirstOrbitDiameter(120);
      // console.log("768px big");
    } else {
      setFirstOrbitDiameter(50);
      // console.log("small");
    }
  };

  useEffect(() => {
    // console.log(window?.innerWidth);
    // Add event listener to handle window resize
    window.addEventListener("resize", handleWindowSizeChange);

    // Initial call to set initial value
    handleWindowSizeChange();

    // Clean up the event listener on component unmount
    return () => {
      window.removeEventListener("resize", handleWindowSizeChange);
    };
  }, []);

  // Update marginBetweenOrbits based on screen size
  useEffect(() => {
    const handleResize = () => {
      const windowWidth = window.innerWidth;
      // console.log(windowWidth);
      if (windowWidth >= 1024) {
        setMarginBetweenOrbits(60);
      } else if (windowWidth > 768 && windowWidth < 1024) {
        setMarginBetweenOrbits(35);
      } else if (windowWidth <= 768 && windowWidth < 480) {
        setMarginBetweenOrbits(30);
      } else {
        setMarginBetweenOrbits(30);
      }
    };

    window.addEventListener("resize", handleResize);

    // Call handleResize once on initial load
    handleResize();

    // Clean up event listener on component unmount
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);
  const [showSecondImage, setShowSecondImage] = useState(false);
  useEffect(() => {
    const timer = setInterval(() => {
      setShowSecondImage((prev) => !prev);
    }, 2000);

    return () => clearInterval(timer);
  }, []);
  return (
    <div>
      <div className=" bg-red-400- lg:my-[100px] xs:mb-[100px] sm:mb-0  md:h-[500px] xs:h-[500px]  flex md:justify-around xs:justify-center xs:items-center xs:flex-col-reverse sm:flex-row     ">
        <div className=" relative xl:ml-[60px] xs:flex xs:justify-center xs:items-center  md:w-[450px]- lg:w-[700px] md:h-[500px]- xs:w-[290px] xs:h-[300px] xs:my-[40px]-   ">
          <ReactOrbits
            firstOrbitDiameter={firstOrbitDiameter}
            marginBetweenOrbits={marginBetweenOrbits}
          >
            <Orbit
              animationSpeedInSeconds={6.5}
              borderColor="#D9D9D9"
              borderSize={1}
              degrees={90}
            >
              <Planet
                backgroundImageURL={Images.ReactSvg}
                size={40}
                spin="left"
              />
            </Orbit>
            <Orbit
              animationSpeedInSeconds={7.0}
              borderColor="#D9D9D9"
              borderSize={1}
              spin="right"
              degrees={90}
            >
              <Planet backgroundImageURL={Images.WEB3} size={40} spin="left" />
              <Planet
                backgroundImageURL={Images.illustrator}
                size={40}
                spin="left"
              />
            </Orbit>
            <Orbit
              animationSpeedInSeconds={7.5}
              borderColor="#D9D9D9"
              borderSize={1}
              degrees={-90}
              spin="right"
            >
              <Planet
                backgroundImageURL={Images.JAVA}
                size={40}
                borderColor="#D9D9D9"
                spin="left"
              />
              <Planet backgroundImageURL={Images.Figma} size={40} spin="left" />
              <Planet
                backgroundImageURL={Images.Aws}
                size={40}
                borderColor="#D9D9D9"
                spin="left"
                className="atom_orbit_aws"
              />
              <Planet
                backgroundImageURL={Images.EXPRESS}
                size={40}
                borderColor="#D9D9D9"
                spin="left"
              />
            </Orbit>
            <Orbit
              animationSpeedInSeconds={8.0}
              borderSize={1}
              degrees={90}
              borderColor="#D9D9D9"
              spin="right"
            >
              <Planet backgroundImageURL={Images.JS} size={40} spin="left" />
              <Planet
                backgroundImageURL={Images.photoshop}
                size={40}
                borderColor="#D9D9D9"
                spin="left"
                className="atom_orbit_photoshop"
              />
              <Planet
                backgroundImageURL={Images.Typescript}
                size={40}
                borderColor="#D9D9D9"
                spin="left"
                className="atom_orbit_typescript"
              />
              <Planet
                backgroundImageURL={Images.Python}
                size={40}
                borderColor="#D9D9D9"
                spin="left"
              />
            </Orbit>
          </ReactOrbits>
          <div className="lg:block xs:hidden">
            <Image
              src={Images.ShadowTwo}
              alt="animated logo"
              fill
              className="opacity-[0.3] -z-20  drop-shadow-[0_100px_50px_rgba(143,66,66,1)]- "
            />
          </div>
        </div>
        <motion.div
          //  style ={{ scale }}

          ref={containerRef}
          className="relative flex justify-center  items-center  flex-col bg-red-400- lg:w-[700px] h-[400px]  "
        >
          <div className=" xs:text-center md:text-left xs:mt-4- md:ml-[20px] flex justify-center items-center  flex-col ">
            <motion.div
              className="img-inner"
              // style={
              //   !windows
              //     ? { translateY: imageValueOn }
              //     : { translateX: imageValue }
              // }
            >
              <div className=" lg:block xs:hidden ">
                <Image
                  src={Images.elipse}
                  alt="animated logo"
                  fill
                  className="  opacity-[0.2] -z-[20] ml-[50px]   "
                />
              </div>
              <div className="">
                <LabelComponent
                  className="xl:text-[70px] lg:text-[50px] -z-10 md:text-[30px] xs:text-[40px] text-Contessa font-bold font-outfit not-italic leading-normal "
                  label={Strings.tecnology}
                />
                <LabelComponent
                  className="xl:text-[45px] lg:text-[35px]  md:text-[30px] xs:text-[34px] text-white font-medium font-outfit not-italic  leading-normal "
                  label={Strings.Our}
                />
              </div>
            </motion.div>
            <motion.div
              className="img-inner"
              // style={{ translateY: imageValueOne }}
            >
              <div className="border-b border-Contessa md:w-[130px] lg:w-[160px] xs:w-[100px] md:pb-[24px] xs:pb-2"></div>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default CircleRounded;
