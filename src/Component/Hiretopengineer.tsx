import { AnimatePresence, motion } from "framer-motion";
import { Images, Skills, Strings } from "../constant";
import React, { useEffect, useRef, useState } from "react";

import { ArrowLeftIcon } from "@heroicons/react/24/outline";
import { CloseOutlined } from "@ant-design/icons";
import Image from "next/image";
import { Line } from "rc-progress";
import Loading from "./Loading";
import Optionmodal from "./Optionmodal";
import Swal from "sweetalert2";
import axios from "axios";

// Skill normalization and fuzzy matching utilities
const normalizeSkill = (skill: string): string => {
  return skill
    .toLowerCase()
    .replace(/[^a-z0-9]/g, '') // Remove special characters and spaces
    .trim();
};

const calculateSimilarity = (str1: string, str2: string): number => {
  const longer = str1.length > str2.length ? str1 : str2;
  const shorter = str1.length > str2.length ? str2 : str1;

  if (longer.length === 0) return 1.0;

  const editDistance = levenshteinDistance(longer, shorter);
  return (longer.length - editDistance) / longer.length;
};

const levenshteinDistance = (str1: string, str2: string): number => {
  const matrix = [];

  for (let i = 0; i <= str2.length; i++) {
    matrix[i] = [i];
  }

  for (let j = 0; j <= str1.length; j++) {
    matrix[0][j] = j;
  }

  for (let i = 1; i <= str2.length; i++) {
    for (let j = 1; j <= str1.length; j++) {
      if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
        matrix[i][j] = matrix[i - 1][j - 1];
      } else {
        matrix[i][j] = Math.min(
          matrix[i - 1][j - 1] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j] + 1
        );
      }
    }
  }

  return matrix[str2.length][str1.length];
};

export const toggleModal = (
  isOpen: boolean,
  setIsOpen: {
    (value: React.SetStateAction<boolean>): void;
    (arg0: boolean): void;
  }
) => {
  setIsOpen(!isOpen);
};

const Hiretopengineer = ({
  isOpen,
  setIsOpen,
}: {
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const modalRef = useRef<HTMLDivElement>(null);
  const [worktype, setWorktype] = useState<string | null>(null);
  const [noOfSoftEngineer, setnoOfSoftEngineer] = useState<string | null>(null);
  const [noOfEmployee, setnoOfEmployee] = useState<string | null>(null);
  const [skills, setSkills] = useState<number[]>([]);
  const [findUs, setFindUs] = useState<number[]>([]);
  const [isOtherSelected, setIsOtherSelected] = useState(false);
  const [isOtherSelected2, setIsOtherSelected2] = useState(false);
  const [otherText, setOtherText] = useState("");
  const [otherText2, setOtherText2] = useState("");
  const [additionalOptions, setAdditionalOptions] = useState([]);
  const [additionalOptions2, setAdditionalOptions2] = useState([]);
  const [firstName, setfirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [companyEmail, setcompanyEmail] = useState("");
  const [optionCounter, setOptionCounter] = useState(10);
  const [optionCounter1, setOptionCounter1] = useState(7);
  const [message, setMessage] = useState("");
  const [activeModalPage, setActiveModalPage] = useState(1);
  const [selectedSkills, setSelectedSkills] = useState<string[]>([]);
  const [selectedFind, setSelectedFind] = useState<string[]>([]);
  const progress = (activeModalPage / 9) * 100;
  const [isLoading, setIsLoading] = useState(false);
  const [animating, setAnimating] = useState(false);
  const [error, setError] = useState("");
  const [skillSuggestion, setSkillSuggestion] = useState<string | null>(null);
  const [showSuggestion, setShowSuggestion] = useState(false);
  
    // Known skills database - add more as needed
  const knownSkills = [
    "ReactJS", "React.js", "React",
    "JavaScript", "Javascript", "JS",
    "Node.Js", "NodeJS", "Node",
    "Express.js", "ExpressJS", "Express",
    "Python", "Python3",
    "React Native", "ReactNative",
    "AWS", "Amazon Web Services",
    "BlockChain", "Block Chain",
    "TypeScript", "Typescript", "TS",
    "Angular", "AngularJS", "Angular.js",
    "Vue.js", "VueJS", "Vue",
    "MongoDB", "Mongo DB", "Mongo",
    "PostgreSQL", "Postgres", "PostgresQL",
    "MySQL", "My SQL",
    "Docker", "Docker Container",
    "Kubernetes", "K8s",
    "GraphQL", "Graph QL",
    "Redux", "Redux.js",
    "Next.js", "NextJS", "Next",
    "Figma", "Figma Design"
  ];

  const findBestMatch = (inputSkill: string): string | null => {
    const normalizedInput = normalizeSkill(inputSkill);
    let bestMatch = null;
    let bestSimilarity = 0;
    const threshold = 0.7; // 70% similarity threshold

    for (const knownSkill of knownSkills) {
      const normalizedKnown = normalizeSkill(knownSkill);
      const similarity = calculateSimilarity(normalizedInput, normalizedKnown);

      if (similarity > bestSimilarity && similarity >= threshold) {
        bestSimilarity = similarity;
        bestMatch = knownSkill;
      }
    }

    return bestMatch;
  };

  const isValidSkill = (skill: string): boolean => {
    // Remove extra spaces and check if it's not empty
    const trimmedSkill = skill.trim();
    if (!trimmedSkill) return false;

    // Check minimum length (at least 2 characters)
    if (trimmedSkill.length < 2) return false;

    // Check if it contains at least one letter
    if (!/[a-zA-Z]/.test(trimmedSkill)) return false;

    // Check if it's not just numbers or special characters
    if (/^[^a-zA-Z]*$/.test(trimmedSkill)) return false;

    // Check for common non-skill patterns
    const invalidPatterns = [
      /^[0-9]+$/, // Only numbers
      /^[!@#$%^&*(),.?":{}|<>]+$/, // Only special characters
      /^\s+$/, // Only whitespace
      /^(test|testing|example|sample|demo|placeholder)$/i, // Common placeholder words
      /^(a|an|the|and|or|but|in|on|at|to|for|of|with|by)$/i, // Common articles/prepositions
    ];

    return !invalidPatterns.some(pattern => pattern.test(trimmedSkill));
  };
  const [optionModalData, setOptionModalData] = useState([
    {
      id: 1,
      buttonText: "Button 1 Text",
      label: "ReactJS",
      imageSrc: Images.ReactSvg,
    },
    {
      id: 2,
      buttonText: "Button 2 Text",
      label: "Figma",
      imageSrc: Images.Figma,
    },
    {
      id: 3,
      buttonText: "Button 2 Text",
      label: "Javascript",
      imageSrc: Images.javascrit,
    },
    {
      id: 4,
      buttonText: "Button 2 Text",
      label: "Node.Js",
      imageSrc: Images.JS,
    },
    {
      id: 5,
      buttonText: "Button 2 Text",
      label: "Python",
      imageSrc: Images.Python,
    },
    {
      id: 6,
      buttonText: "Button 2 Text",
      label: "React Native",
      imageSrc: Images.atom,
    },
    {
      id: 7,
      buttonText: "Button 2 Text",
      label: "AWS",
      imageSrc: Images.Aws,
    },
    {
      id: 8,
      buttonText: "Button 2 Text",
      label: "Blockchain",
      imageSrc: Images.Blockchain,
    },
    ...additionalOptions,
    {
      id: 9,
      buttonText: "Button 2 Text",
      label: "Other",
    },
  ]);
  const [optionModalData2, setOptionModalData2] = useState([
    {
      id: 1,
      buttonText: "Button 1 Text",
      label: "Twitter",
      imageSrc: Images.twitter,
    },
    {
      id: 2,
      buttonText: "Button 2 Text",
      label: "Linkedin",
      imageSrc: Images.linkedin,
    },
    {
      id: 3,
      buttonText: "Button 2 Text",
      label: "Google",
      imageSrc: Images.google,
    },
    {
      id: 4,
      buttonText: "Button 2 Text",
      label: "Clutch",
      imageSrc: Images.clutch,
    },
    {
      id: 5,
      buttonText: "Button 2 Text",
      label: "Instagram",
      imageSrc: Images.Insta,
    },
    ...additionalOptions2,
    {
      id: 6,
      buttonText: "Button 2 Text",
      label: "Other",
    },
  ]);

  const handleSubmit = async () => {
    try {
      setIsLoading(true);
      if (!findUs.length) {
        setError("Please let us know where did you find us.");
        setIsLoading(false);
        return;
      }
      const filteredSkills = selectedSkills.filter(
        (label) => label !== "Other"
      );
      const filteredFindUs = selectedFind.filter((label) => label !== "Other");

      const payload: Record<string, any> = {
        workType: worktype,
        noOfSoftEngineer: noOfSoftEngineer,
        firstName: firstName,
        lastName: lastName,
        companyEmail: companyEmail,
        noOfEmployee: noOfEmployee,
        findUs: filteredFindUs,
      };
      if (selectedSkills?.length) {
        payload.skill = filteredSkills;
      }
      if (message.trim()) {
        payload.message = message;
      }
      const data = JSON.stringify(payload);
      // console.log(data, "dataaaaaa");
      let config = {
        method: "post",
        maxBodyLength: Infinity,
        url: `${process.env.NEXT_PUBLIC_API_URL}hiretopengineer/addHireData`,
        headers: {
          Authorization:
            "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoib3B0b3hfMDAwMDAwMSIsInRva2VuX3ZlcnNpb24iOjg1LCJpYXQiOjE2OTE4NDQwNTAsImV4cCI6MTY5MTg0NzY1MH0.IDhGTQU8feTBM67SRj_Rmg0ohXVPRnZWK_jm3MqqUhY",
          "Content-Type": "application/json",
        },
        data: data,
      };
      const response = await axios.request(config);
      if (response) {
        Swal.fire({
          toast: true,
          position: "top-end",
          showConfirmButton: false,
          timer: 3000,
          icon: "success",
          title:
            "Your request to hire a engineer has been submitted successfully.",
          padding: "10px 20px",
          customClass: {
            popup: "swal-custom-toast",
          },
        });
        setActiveModalPage(activeModalPage + 1);
      }
    } catch (error) {
      console.log(error);
      Swal.fire({
        toast: true,
        position: "top-end",
        showConfirmButton: false,
        timer: 3000,
        icon: "error",
        title: "Something went wrong!",
        padding: "10px 20px",
        customClass: {
          popup: "swal-custom-toast",
        },
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleOptionClick = (data: { id: number; label: string }) => {
    let newSelectedOptions = [...skills];
    if (data.id === 9) {
      // Handle "Other" option - don't add to selectedSkills immediately
      setIsOtherSelected(!isOtherSelected);
      if (!isOtherSelected) {
        newSelectedOptions = [...newSelectedOptions, data.id];
      } else {
        newSelectedOptions = newSelectedOptions.filter(
          (skill) => skill !== data.id
        );
        // Clear the input when deselecting "Other"
        setOtherText("");
      }
      setSkills(newSelectedOptions);
      // Don't add "Other" to selectedSkills - it will be added when user actually adds a skill
    } else {
      // Handle both predefined skills and custom skills
      const isCurrentlySelected = newSelectedOptions.includes(data.id);

      if (isCurrentlySelected) {
        // Deselecting the skill
        newSelectedOptions = newSelectedOptions.filter((skill) => skill !== data.id);

        // If it's a custom skill (id > 8), remove it from optionModalData as well
        if (data.id > 8) {
          setOptionModalData((prevData) =>
            prevData.filter(option => option.id !== data.id)
          );

          // Show success message for custom skill removal
          Swal.fire({
            toast: true,
            position: "top-end",
            showConfirmButton: false,
            timer: 2000,
            icon: "success",
            title: `"${data.label}" removed successfully!`,
            padding: "10px 20px",
            customClass: {
              popup: "swal-custom-toast",
            },
          });
        }
      } else {
        // Selecting the skill
        newSelectedOptions = [...newSelectedOptions, data.id];
      }

      setSkills(newSelectedOptions);

      let newSelectedSkills = [...selectedSkills];
      newSelectedSkills = newSelectedSkills.includes(data.label)
        ? newSelectedSkills.filter((skill) => skill !== data.label)
        : [...newSelectedSkills, data.label];
      setSelectedSkills(newSelectedSkills);
    }
  };

  const handleAddButtonClick = () => {
    if (isOtherSelected && otherText) {
      // Validate if the input is a valid skill
      if (!isValidSkill(otherText)) {
        Swal.fire({
          toast: true,
          position: "top-end",
          showConfirmButton: false,
          timer: 4000,
          icon: "error",
          title: `"${otherText}" doesn't appear to be a valid skill. Please enter a technology, programming language, framework, or tool name.`,
          padding: "10px 20px",
          customClass: {
            popup: "swal-custom-toast",
          },
        });
        setOtherText("");
        return;
      }

      // Get predefined skill labels (excluding "Other")
      const predefinedSkills = optionModalData
        .filter(option => option.id <= 8 && option.label !== "Other")
        .map(option => option.label);

      // Check if the input matches any predefined skill (case-insensitive)
      const isPredefinedSkill = predefinedSkills.some(
        skill => skill.toLowerCase() === otherText.toLowerCase()
      );

      if (isPredefinedSkill) {
        const matchedSkill = predefinedSkills.find(
          skill => skill.toLowerCase() === otherText.toLowerCase()
        );
        Swal.fire({
          toast: true,
          position: "top-end",
          showConfirmButton: false,
          timer: 4000,
          icon: "info",
          title: `"${matchedSkill}" is already available as a predefined skill. Please select it from the buttons above.`,
          padding: "10px 20px",
          customClass: {
            popup: "swal-custom-toast",
          },
        });
        setOtherText("");
        return;
      }

      // Check for fuzzy match
      const bestMatch = findBestMatch(otherText);

      if (bestMatch && bestMatch.toLowerCase() !== otherText.toLowerCase()) {
        // Show suggestion
        setSkillSuggestion(bestMatch);
        setShowSuggestion(true);
        return;
      }

      // Use the original text or the exact match
      const skillToAdd = bestMatch || otherText;

      // Check for duplicates (case-insensitive)
      const isDuplicate = selectedSkills.some(
        skill => skill.toLowerCase() === skillToAdd.toLowerCase()
      );

      if (isDuplicate) {
        Swal.fire({
          toast: true,
          position: "top-end",
          showConfirmButton: false,
          timer: 3000,
          icon: "warning",
          title: `"${skillToAdd}" is already added!`,
          padding: "10px 20px",
          customClass: {
            popup: "swal-custom-toast",
          },
        });
        setOtherText("");
        return;
      }

      const newOption = {
        id: optionCounter,
        buttonText: "Button Text",
        label: skillToAdd,
      };
      setOptionCounter((prevCounter) => prevCounter + 1);
      const updatedOptionModalData = [
        ...optionModalData.slice(0, 8),
        newOption,
        ...optionModalData.slice(8),
      ];
      setOptionModalData(updatedOptionModalData);
      setSkills((prevSkills) => [...prevSkills, newOption.id]);
      setSelectedSkills((prevSelectedSkills) => [
        ...prevSelectedSkills,
        newOption.label,
      ]);
      setIsOtherSelected(false);
      setOtherText("");
      setShowSuggestion(false);
      setSkillSuggestion(null);
    }
  };

  const acceptSuggestion = () => {
    if (skillSuggestion) {
      // Get predefined skill labels (excluding "Other")
      const predefinedSkills = optionModalData
        .filter(option => option.id <= 8 && option.label !== "Other")
        .map(option => option.label);

      // Check if the suggestion matches any predefined skill (case-insensitive)
      const isPredefinedSkill = predefinedSkills.some(
        skill => skill.toLowerCase() === skillSuggestion.toLowerCase()
      );

      if (isPredefinedSkill) {
        const matchedSkill = predefinedSkills.find(
          skill => skill.toLowerCase() === skillSuggestion.toLowerCase()
        );
        Swal.fire({
          toast: true,
          position: "top-end",
          showConfirmButton: false,
          timer: 4000,
          icon: "info",
          title: `"${matchedSkill}" is already available as a predefined skill. Please select it from the buttons above.`,
          padding: "10px 20px",
          customClass: {
            popup: "swal-custom-toast",
          },
        });
        setIsOtherSelected(false);
        setOtherText("");
        setShowSuggestion(false);
        setSkillSuggestion(null);
        return;
      }

      // Check for duplicates (case-insensitive)
      const isDuplicate = selectedSkills.some(
        skill => skill.toLowerCase() === skillSuggestion.toLowerCase()
      );

      if (isDuplicate) {
        Swal.fire({
          toast: true,
          position: "top-end",
          showConfirmButton: false,
          timer: 3000,
          icon: "warning",
          title: `"${skillSuggestion}" is already added!`,
          padding: "10px 20px",
          customClass: {
            popup: "swal-custom-toast",
          },
        });
        setIsOtherSelected(false);
        setOtherText("");
        setShowSuggestion(false);
        setSkillSuggestion(null);
        return;
      }

      const newOption = {
        id: optionCounter,
        buttonText: "Button Text",
        label: skillSuggestion,
      };
      setOptionCounter((prevCounter) => prevCounter + 1);
      const updatedOptionModalData = [
        ...optionModalData.slice(0, 8),
        newOption,
        ...optionModalData.slice(8),
      ];
      setOptionModalData(updatedOptionModalData);
      setSkills((prevSkills) => [...prevSkills, newOption.id]);
      setSelectedSkills((prevSelectedSkills) => [
        ...prevSelectedSkills,
        newOption.label,
      ]);
      setIsOtherSelected(false);
      setOtherText("");
      setShowSuggestion(false);
      setSkillSuggestion(null);
    }
  };

  const rejectSuggestion = () => {
    // Validate if the original text is a valid skill
    if (!isValidSkill(otherText)) {
      Swal.fire({
        toast: true,
        position: "top-end",
        showConfirmButton: false,
        timer: 4000,
        icon: "error",
        title: `"${otherText}" doesn't appear to be a valid skill. Please enter a technology, programming language, framework, or tool name.`,
        padding: "10px 20px",
        customClass: {
          popup: "swal-custom-toast",
        },
      });
      setIsOtherSelected(false);
      setOtherText("");
      setShowSuggestion(false);
      setSkillSuggestion(null);
      return;
    }

    // Get predefined skill labels (excluding "Other")
    const predefinedSkills = optionModalData
      .filter(option => option.id <= 8 && option.label !== "Other")
      .map(option => option.label);

    // Check if the original text matches any predefined skill (case-insensitive)
    const isPredefinedSkill = predefinedSkills.some(
      skill => skill.toLowerCase() === otherText.toLowerCase()
    );

    if (isPredefinedSkill) {
      const matchedSkill = predefinedSkills.find(
        skill => skill.toLowerCase() === otherText.toLowerCase()
      );
      Swal.fire({
        toast: true,
        position: "top-end",
        showConfirmButton: false,
        timer: 4000,
        icon: "info",
        title: `"${matchedSkill}" is already available as a predefined skill. Please select it from the buttons above.`,
        padding: "10px 20px",
        customClass: {
          popup: "swal-custom-toast",
        },
      });
      setIsOtherSelected(false);
      setOtherText("");
      setShowSuggestion(false);
      setSkillSuggestion(null);
      return;
    }

    // Check for duplicates (case-insensitive)
    const isDuplicate = selectedSkills.some(
      skill => skill.toLowerCase() === otherText.toLowerCase()
    );

    if (isDuplicate) {
      Swal.fire({
        toast: true,
        position: "top-end",
        showConfirmButton: false,
        timer: 3000,
        icon: "warning",
        title: `"${otherText}" is already added!`,
        padding: "10px 20px",
        customClass: {
          popup: "swal-custom-toast",
        },
      });
      setIsOtherSelected(false);
      setOtherText("");
      setShowSuggestion(false);
      setSkillSuggestion(null);
      return;
    }

    // Use original text
    const newOption = {
      id: optionCounter,
      buttonText: "Button Text",
      label: otherText,
    };
    setOptionCounter((prevCounter) => prevCounter + 1);
    const updatedOptionModalData = [
      ...optionModalData.slice(0, 8),
      newOption,
      ...optionModalData.slice(8),
    ];
    setOptionModalData(updatedOptionModalData);
    setSkills((prevSkills) => [...prevSkills, newOption.id]);
    setSelectedSkills((prevSelectedSkills) => [
      ...prevSelectedSkills,
      newOption.label,
    ]);
    setIsOtherSelected(false);
    setOtherText("");
    setShowSuggestion(false);
    setSkillSuggestion(null);
  };

  const handleOptionClick2 = (data: { id: number; label: string }) => {
    let newSelectedOptions2 = [...findUs];
    if (data.id === 6) {
      setIsOtherSelected2(!isOtherSelected2);
      if (!isOtherSelected2) {
        newSelectedOptions2 = [...newSelectedOptions2, data.id];
      } else {
        newSelectedOptions2 = newSelectedOptions2.filter(
          (findUs) => findUs !== data.id
        );
      }
    } else {
      newSelectedOptions2 = newSelectedOptions2.includes(data.id)
        ? newSelectedOptions2.filter((findUs) => findUs !== data.id)
        : [...newSelectedOptions2, data.id];
    }
    setFindUs(newSelectedOptions2);
    let newSelectedFind = [...selectedFind];
    if (data.id === 6) {
      if (!isOtherSelected) {
        newSelectedFind = [...newSelectedFind, data.label];
      } else {
        newSelectedFind = newSelectedFind.filter(
          (skill) => skill !== data.label
        );
      }
    } else {
      newSelectedFind = newSelectedFind.includes(data.label)
        ? newSelectedFind.filter((findUs) => findUs !== data.label)
        : [...newSelectedFind, data.label];
    }
    setSelectedFind(newSelectedFind);
  };

  const handleAddButtonClick2 = () => {
    if (isOtherSelected2 && otherText2) {
      const newOption = {
        id: optionCounter1,
        buttonText: "Button Text",
        label: otherText2,
      };
      setOptionCounter1((prevCounter) => prevCounter + 1);
      const updatedOptionModalData = [
        ...optionModalData2.slice(0, 5),
        newOption,
        ...optionModalData2.slice(5),
      ];
      setOptionModalData2(updatedOptionModalData);
      setFindUs((prevFindUs) => [...prevFindUs, newOption.id]);
      setSelectedFind((prevSelectedFindUs) => [
        ...prevSelectedFindUs,
        newOption.label,
      ]);
      setIsOtherSelected2(false);
      setOtherText2("");
    }
  };
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "";
    }
  }, [isOpen]);

  const openModal = () => {
    setAnimating(true);
    setTimeout(() => {
      setIsOpen(false);
      setAnimating(false);
    }, 500);
  };
  const handleClickOutside = (event: MouseEvent) => {
    if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
      openModal();
    }
  };
  useEffect(() => {
    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [isOpen]);

  const handleNext = () => {
    let errorMessage = "";

    switch (activeModalPage) {
      case 1:
        if (!worktype) {
          errorMessage = "Please select an option before proceeding.";
        }
        break;
      case 3:
        if (!noOfSoftEngineer) {
          errorMessage = "Please select the number of software engineers.";
        }
        break;
      case 4:
        if (!firstName.trim() || !lastName.trim()) {
          errorMessage = "Please fill in both first and last name.";
        }
        break;
      case 5:
        if (
          !companyEmail.trim() ||
          !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(companyEmail)
        ) {
          errorMessage = "Please enter a valid company email address.";
        }
        break;
      case 6:
        if (!noOfEmployee) {
          errorMessage = "Please select the number of employees.";
        }
        break;
      case 8:
        if (!findUs.length && !isOtherSelected2) {
          errorMessage = "Please select an option or add your own.";
        } else if (isOtherSelected2 && !otherText2.trim()) {
          errorMessage =
            "Please fill in the 'Other' field or select an option.";
        }
        break;
      default:
        break;
    }

    if (errorMessage) {
      setError(errorMessage);
    } else {
      setError("");
      setActiveModalPage(activeModalPage + 1);
    }
  };

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Enter" && event.shiftKey) {
        return;
      }
      if (event.key === "Enter") {
        if (activeModalPage < 8) {
          event.preventDefault();
          handleNext();
        }
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [
    activeModalPage,
    worktype,
    noOfSoftEngineer,
    firstName,
    lastName,
    companyEmail,
    noOfEmployee,
    findUs,
    isOtherSelected2,
    otherText2,
  ]);

  const worktypes = [
    { label: "Part Time", value: "partTime" },
    { label: "Full Time", value: "fullTime" },
  ];
  const engineers = ["1-2", "2-5", "5+"];
  const employees = ["1-10", "10-50", "50+"];

  const selectWorkType = (worktype: string) => {
    setWorktype((prev) => (prev === worktype ? null : worktype));
  };

  const selectEngineer = (engineer: string) => {
    setnoOfSoftEngineer((prev) => (prev === engineer ? null : engineer));
  };

  const selectEmployees = (employee: string) => {
    setnoOfEmployee((prev) => (prev === employee ? null : employee));
  };

  return (
    <div>
      {(isOpen || animating) && (
        <div className="fixed top-0 left-0 w-full h-full flex justify-center items-end z-40 bg-gray-500 bg-opacity-[20%] backdrop-blur-sm">
          <button
            className={`fixed top-7 right-7 flex h-8 w-8 items-center justify-center rounded-full text-white transition-transform hover:scale-110 transform duration-500 ${
              isOpen || animating ? "opacity-100" : "opacity-0"
            }`}
            onClick={openModal}
          >
            <CloseOutlined rev={undefined} />
          </button>
          <div
            ref={modalRef}
            className={`bg-black font-outfit rounded-t-3xl w-full h-[90%] overflow-y-scroll no-scrollbar transform transition-transform duration-300 
            ${
              isOpen && !animating ? "slide-in" : "slide-out"
            } flex justify-center items-center p-5`}
          >
            <div className="bg-gradient-to-r from-Tosca to-contessa w-full pb-[1px] absolute top-0">
              <div className="relative flex items-center bg-black xs:h-16 md:h-20">
                {activeModalPage > 1 && (
                  <button
                    onClick={() => {
                      setActiveModalPage(activeModalPage - 1);
                      setError("");
                    }}
                    className="absolute left-7 hover:bg-[linear-gradient(to_top,_#241415,_black)] border border-tosca rounded-md flex justify-center gap-x-2 items-center h-10 w-24 text-sm text-white"
                  >
                    <ArrowLeftIcon className="w-5 h-5" />
                    Back
                  </button>
                )}
                <div className="gap-x-2 flex items-center absolute right-7 text-white">
                  <Line
                    percent={progress}
                    strokeWidth={3}
                    trailWidth={3}
                    strokeColor="#009c20"
                    trailColor="#122f17"
                    className="w-40"
                  />
                  <h1>{activeModalPage}/9</h1>
                </div>
              </div>
            </div>
            <div className="text-white">
              <AnimatePresence mode="wait">
                {activeModalPage === 1 && (
                  <motion.div
                    key="page1"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.5, ease: "easeInOut" }}
                  >
                    <div className="gap-y-2">
                      <p className="font-semibold text-white xs:text-lg md:text-2xl">
                        {Strings.QUS_1}
                      </p>
                      <p className="font-normal text-white xs:text-sm md:text-base">
                        {Strings.QUS_1_SUB}
                      </p>
                    </div>
                    <div className="flex gap-4 mt-4">
                      {worktypes.map(({ label, value }) => (
                        <button
                          key={value}
                          className={`relative inset-0 bg-[linear-gradient(to_top,_#241415,_black)] border-tosca border text-white px-6 py-3 rounded-lg shadow-md ${
                            worktype === value
                              ? "border-opacity-100"
                              : "border-opacity-70"
                          }`}
                          onClick={() => {
                            selectWorkType(value);
                            setError("");
                          }}
                        >
                          {label}
                          {worktype === value && (
                            <Image
                              src={Images.Checkicon}
                              alt="Check icon"
                              height={500}
                              width={500}
                              className="h-5 w-5 absolute -top-1 -right-1"
                            />
                          )}
                        </button>
                      ))}
                    </div>
                    {error && (
                      <p className="text-red-500 fixed mt-2 text-sm font-medium">
                        {error}
                      </p>
                    )}
                  </motion.div>
                )}
                {activeModalPage === 2 && (
                  <motion.div
                    key="page2"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.5, ease: "easeInOut" }}
                  >
                    <div className="gap-y-2">
                      <p className="font-semibold text-white xs:text-lg md:text-2xl">
                        {Strings.QUS_2}
                      </p>
                      <p className="font-light text-white xs:text-sm md:text-base">
                        {Strings.QUS_2_SUB}
                      </p>
                    </div>
                    <p className="my-2 text-xs font-light text-white">
                      {Strings.CHOOSE_AS_MANY_AS_YOU_LIKE}
                    </p>
                    <div className="flex flex-wrap items-center gap-4 xs:w-80 md:w-[620px]">
                      {optionModalData.map((data) => (
                        <Optionmodal
                          isImageShow={data.id < 8.5 && data.id !== 9}
                          key={data.id}
                          buttonText={data.buttonText}
                          label={data.label}
                          imageSrc={data.imageSrc}
                          isSelected={
                            data.id === 9
                              ? isOtherSelected
                              : data.id > 8
                              ? true
                              : skills.includes(data.id)
                          }
                          onClick={() => handleOptionClick(data)}
                        />
                      ))}
                      {isOtherSelected && (
                        <div className="flex gap-x-4">
                          <input
                            type="text"
                            placeholder="Enter your other skills here..."
                            className="border-tosca h-12 w-full rounded-lg border p-2 py-3 outline-none bg-black xs:text-sm lg:text-base"
                            value={otherText}
                            onChange={(e) => setOtherText(e.target.value)}
                          />
                          <button
                            onClick={handleAddButtonClick}
                            className="bg-gradient-to-t from-tosca to-contessa rounded-lg text-white flex justify-center items-center h-12 w-24 text-base transition-transform hover:scale-105 transform"
                          >
                            {Strings.ADD}
                          </button>
                        </div>
                      )}
                    </div>
                  </motion.div>
                )}

                {activeModalPage === 3 && (
                  <motion.div
                    key="page3"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.5, ease: "easeInOut" }}
                  >
                    <div className="gap-y-2">
                      <p className="font-semibold text-white xs:text-lg md:text-2xl">
                        {Strings.QUS_3}
                      </p>
                      <p className="font-normal text-white xs:text-sm md:text-base">
                        {Strings.QUS_3_SUB}
                      </p>
                    </div>
                    <div className="flex gap-4 mt-4">
                      {engineers.map((engineer) => (
                        <button
                          key={engineer}
                          className={`relative inset-0 bg-[linear-gradient(to_top,_#241415,_black)] border-tosca border text-white px-6 py-3 rounded-lg shadow-md ${
                            noOfSoftEngineer === engineer
                              ? "border-opacity-100"
                              : "border-opacity-70"
                          }`}
                          onClick={() => {
                            selectEngineer(engineer);
                            setError("");
                          }}
                        >
                          {engineer}
                          {noOfSoftEngineer === engineer && (
                            <Image
                              src={Images.Checkicon}
                              alt="Check icon"
                              height={500}
                              width={500}
                              className="h-5 w-5 absolute -top-1 -right-1"
                            />
                          )}
                        </button>
                      ))}
                    </div>
                    {error && (
                      <p className="text-red-500 fixed mt-2 text-sm font-medium">
                        {error}
                      </p>
                    )}
                  </motion.div>
                )}
                {activeModalPage === 4 && (
                  <motion.div
                    key="page4"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.5, ease: "easeInOut" }}
                  >
                    <div className="w-full">
                      <div className="gap-y-2">
                        <p className="font-semibold text-white xs:text-lg md:text-2xl">
                          {Strings.QUS_4}
                        </p>
                        <p className="font-normal text-white xs:text-sm md:text-base">
                          {Strings.QUS_4_SUB}
                        </p>
                      </div>
                      <div className="mt-4 flex xs:flex-col md:flex-row xs:gap-y-4 md:gap-x-4">
                        <input
                          type="text"
                          placeholder="Enter your first name"
                          className="border-tosca inset-0 bg-[linear-gradient(to_top,_#241415,_black)] h-14 w-full rounded-lg border p-3 outline-none bg-black xs:text-sm lg:text-base"
                          value={firstName}
                          onChange={(e) => {
                            const cleanedValue = e.target.value.replace(
                              /[0-9]/g,
                              ""
                            );
                            setfirstName(cleanedValue);
                            setError("");
                          }}
                        />
                        <input
                          type="text"
                          placeholder="Enter your last name"
                          className="border-tosca inset-0 bg-[linear-gradient(to_top,_#241415,_black)] h-14 w-full rounded-lg border p-3 outline-none bg-black xs:text-sm lg:text-base"
                          value={lastName}
                          onChange={(e) => {
                            const cleanedValue = e.target.value.replace(
                              /[0-9]/g,
                              ""
                            );
                            setLastName(cleanedValue);
                            setError("");
                          }}
                        />
                      </div>
                      {error && (
                        <p className="text-red-500 fixed mt-2 text-sm font-medium">
                          {error}
                        </p>
                      )}
                    </div>
                  </motion.div>
                )}
                {activeModalPage === 5 && (
                  <motion.div
                    key="page5"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.5, ease: "easeInOut" }}
                  >
                    <div className="gap-y-2">
                      <p className="font-semibold text-white xs:text-lg md:text-2xl">
                        {Strings.QUS_6}
                      </p>
                      <p className="font-normal text-white xs:text-sm md:text-base">
                        {Strings.PLEASE_ADD_YOUR_COMPANY_EMAIL}
                      </p>
                    </div>
                    <input
                      type="companyEmail"
                      placeholder="<EMAIL>"
                      className="mt-4 border-tosca inset-0 bg-[linear-gradient(to_top,_#241415,_black)] h-14 w-full rounded-lg border p-3 outline-none bg-black xs:text-sm lg:text-base"
                      value={companyEmail}
                      onChange={(e) => {
                        setcompanyEmail(e.target.value);
                        setError("");
                      }}
                    />
                    {error && (
                      <p className="text-red-500 fixed mt-2 text-sm font-medium">
                        {error}
                      </p>
                    )}
                  </motion.div>
                )}
                {activeModalPage === 6 && (
                  <motion.div
                    key="page6"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.5, ease: "easeInOut" }}
                  >
                    <div className="gap-y-2">
                      <p className="font-semibold text-white xs:text-lg md:text-2xl">
                        {Strings.QUS_7}
                      </p>
                      <p className="font-normal text-white xs:text-sm md:text-base">
                        {Strings.WE_TAILOR_OUR}
                      </p>
                    </div>
                    <div className="flex gap-4 mt-4">
                      {employees.map((employee) => (
                        <button
                          key={employee}
                          className={`relative inset-0 bg-[linear-gradient(to_top,_#241415,_black)] border-tosca border text-white px-6 py-3 rounded-lg shadow-md ${
                            noOfEmployee === employee
                              ? "border-opacity-100"
                              : "border-opacity-70"
                          }`}
                          onClick={() => {
                            selectEmployees(employee);
                            setError("");
                          }}
                        >
                          {employee}
                          {noOfEmployee === employee && (
                            <Image
                              src={Images.Checkicon}
                              alt="Check icon"
                              height={500}
                              width={500}
                              className="h-5 w-5 absolute -top-1 -right-1"
                            />
                          )}
                        </button>
                      ))}
                    </div>
                    {error && (
                      <p className="text-red-500 fixed mt-2 text-sm font-medium">
                        {error}
                      </p>
                    )}
                  </motion.div>
                )}
                {activeModalPage === 7 && (
                  <motion.div
                    key="page7"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.5, ease: "easeInOut" }}
                  >
                    <div className="gap-y-2">
                      <p className="font-semibold text-white xs:text-lg md:text-2xl">
                        {Strings.QUS_8}
                      </p>
                      <p className="font-normal text-white xs:text-sm md:text-base">
                        {Strings.FELL_FREE_TO_TELL}
                      </p>
                    </div>
                    <textarea
                      id="message"
                      rows={3}
                      className="mt-4 border-tosca inset-0 bg-[linear-gradient(to_top,_#241415,_black)] w-full rounded-lg border p-3 outline-none bg-black xs:text-sm lg:text-base"
                      placeholder="Type your answer here..."
                      name="Massage"
                      value={message}
                      onChange={(e) => setMessage(e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === "Enter" && e.shiftKey) {
                          e.preventDefault();
                          setMessage((prev) => `${prev}\n`);
                        }
                      }}
                    ></textarea>
                    <p className="text-xs font-normal text-white">
                      {Strings.SHIFT_ENTER}
                    </p>
                  </motion.div>
                )}
                {activeModalPage === 8 && (
                  <motion.div
                    key="page8"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.5, ease: "easeInOut" }}
                  >
                    <div className="gap-y-2">
                      <p className="font-semibold text-white xs:text-lg md:text-2xl">
                        {Strings.QUS_9}
                      </p>
                      <p className="font-normal text-white xs:text-sm md:text-base">
                        {Strings.WE_APPRECIATE_IT}
                      </p>
                    </div>
                    <div className="mt-5 flex flex-wrap items-center gap-4 xs:w-80 md:w-[620px]">
                      {optionModalData2.map((data) => (
                        <Optionmodal
                          isImageShow={data.id < 5.5 && data.id !== 6}
                          key={data.id}
                          buttonText={data.buttonText}
                          label={data.label}
                          imageSrc={data.imageSrc}
                          isSelected={
                            data.id === 6
                              ? isOtherSelected2
                              : data.id > 5
                              ? true
                              : findUs.includes(data.id)
                          }
                          onClick={() => {
                            handleOptionClick2(data);
                            setError("");
                          }}
                        />
                      ))}
                      {isOtherSelected2 && (
                        <div className="flex gap-x-4">
                          <input
                            type="text"
                            placeholder="Enter your other skills here..."
                            className="border-tosca h-12 w-full rounded-lg border p-4 outline-none bg-black xs:text-sm lg:text-base"
                            value={otherText2}
                            onChange={(e) => {
                              setOtherText2(e.target.value);
                              setError("");
                            }}
                          />
                          <button
                            onClick={handleAddButtonClick2}
                            className="bg-gradient-to-t from-tosca to-contessa rounded-lg text-white flex justify-center items-center h-12 w-24 text-base transition-transform hover:scale-105 transform"
                          >
                            {Strings.ADD}
                          </button>
                        </div>
                      )}
                    </div>
                    {error && (
                      <p className="text-red-500 fixed mt-2 text-sm font-medium">
                        {error}
                      </p>
                    )}
                  </motion.div>
                )}
                {activeModalPage === 9 && (
                  <motion.div
                    key="page9"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.5, ease: "easeInOut" }}
                  >
                    <div className="gap-y-4 text-center">
                      <div className="text-transparent bg-gradient-to-tr from-tosca to-contessa bg-clip-text font-semibold xs:text-2xl md:text-4xl">
                        {Strings.THANK_YOU_FOR}
                      </div>
                      <div className="text-transparent bg-gradient-to-tr from-tosca to-contessa bg-clip-text font-medium xs:text-xl md:text-2xl">
                        {Strings.THE_FORM}
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
            <div className="bg-gradient-to-l from-Tosca to-contessa absolute bottom-0 w-full pt-[1px]">
              <div className="flex bg-black justify-end items-center xs:h-16 md:h-20 px-7">
                {activeModalPage === 8 ? (
                  <button
                    onClick={() => handleSubmit()}
                    className="transition-transform hover:scale-110 transform bg-gradient-to-t from-tosca to-contessa rounded-full flex justify-center gap-2 items-center h-10 w-24 text-sm text-white "
                  >
                    {isLoading ? <Loading /> : "Submit"}
                  </button>
                ) : (
                  <>
                    {activeModalPage < 9 && (
                      <button
                        onClick={handleNext}
                        className="transition-transform hover:scale-110 transform duration-300 bg-gradient-to-t from-Contessa to-Tosca  hover:bg-gradient-to-b  hover:from-Contessa  hover:to-Tosca  rounded-full flex justify-center gap-2 items-center h-10 w-24 text-sm text-white"
                      >
                        Next
                      </button>
                    )}
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Hiretopengineer;
