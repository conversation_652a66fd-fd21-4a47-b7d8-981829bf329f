import { Images, Strings } from "@/constant";
import { useEffect, useState } from "react";

import Image from "next/image";
import { LabelComponent } from "./label";
import React from "react";
import { motion, useTransform, useViewportScroll } from "framer-motion";
import { useRef } from "react";

const AnimatedCircle = () => {
  const containerRef = useRef(null);
  const imagesAndLabels = [
    {
      id: 0,
      image: Images.componen50,
      label: Strings.Work,
      filledIcon: Images.property,
    },
    {
      id: 1,
      image: Images.component51,
      label: Strings.Hire,
      filledIcon: Images.propertyThree,
    },
    {
      id: 2,
      image: Images.componenttwo,
      label: Strings.Mangement,
      filledIcon: Images.propertytwo,
    },
    {
      id: 3,
      image: Images.component,
      label: Strings.global,
      filledIcon: Images.propertyOne,
    },
  ];

  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  const { scrollYProgress } = useViewportScroll()
  const scale = useTransform(scrollYProgress, [0, 0.9], [0.9, 1]);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentImageIndex(
        (prevIndex: number) => (prevIndex + 1) % imagesAndLabels.length
      );
    }, 2000);

    return () => {
      clearInterval(timer);
    };
  });

  return (
    <div ref={containerRef}>
      <div>
        <div className=" bg-yellow-900- lg:mt-[100px]- md:mt-[20px]- flex xs:flex-col md:flex-row md:ml-6  xs:my-[40px]- ">
          <div>
            <div className="relative lg:ml-4 md:ml-0  ">
              <div
                style={{
                  backgroundImage: `url('${Images.circle}') `,
                  width: "",
                  backgroundSize: "contain",
                  backgroundPosition: "left",
                  backgroundRepeat: "no-repeat",
                  zIndex: "1",
                }}
                className="xs:hidden md:block animate-spin   xl:h-[650px] xl:w-[650px] lg:h-[500px] lg:w-[500px]  md:h-[400px] md:w-[400px]  "
              ></div>
              <div
                // style={{ scale }}
              >
                <div
                  className="xs:flex xs:justify-center xs:items-center xs:flex-col xs:w-full  text-center">
                  <div className="text-white font-outfit xl:text-[50px] md:text-[40px]  xs:text-[40px] leading-normal not-italic font-bold md:absolute  lg:top-[14rem] lg:left-[50px] md:top-[8rem]  md:left-[50px]  ">
                    <div
                      className="img-inner"
                    // style={{ translateY: imageValueOne }}
                    >
                      <LabelComponent label={Strings.our} />
                    </div>
                  </div>
                  <div className="text-Contessa font-outfit xl:text-[70px] xl:mt-[10px] md:text-[50px] xs:text-[50px] leading-normal not-italic font-bold md:absolute lg:top-[17rem] lg:left-[50px] md:top-[10rem]  md:left-[50px]    ">
                    <div
                      className="img-inner"
                    // style={{ translateY: imageValueOne }}
                    >
                      <LabelComponent label={Strings.Comptency} />
                    </div>
                    <div className="border-b border-Contessa w-[200px]  xs:pb-4 xs: md:pb-8"></div>
                  </div>
                </div>
              </div>

              <div className="xs:hidden md:block">
                <div>
                  <Image
                    src={Images.gruop}
                    alt="animated logo"
                    width={30}
                    height={30}
                    className="absolute xl:top-[141px] xl:right-[34px] lg:top-[55px] lg:right-[55px] md:top-[32px] md:right-[47px]"
                  />
                </div>
                <div>
                  <Image
                    src={Images.gruop}
                    alt="animated logo"
                    width={30}
                    height={30}
                    className="absolute xl:top-[18rem] xl:right-[-12px]  lg:top-[10rem] lg:right-[0rem] md:top-[7rem] md:right-[0rem]"
                  />
                </div>
                <div>
                  <Image
                    src={Images.gruop}
                    alt="animated logo"
                    width={30}
                    height={30}
                    className="absolute xl:top-[26rem] xl:right-[3px] lg:top-[17rem] lg:right-[-8px] md:top-[13rem] md:right-[-11px]"
                  />
                </div>
                <div>
                  <Image
                    src={Images.gruop}
                    alt="animated logo"
                    width={30}
                    height={30}
                    className="absolute xl:top-[34rem] xl:right-[85px] lg:top-[24rem] lg:right-[39px] md:top-[19rem] md:right-[23px]"
                  />
                </div>
              </div>
            </div>
          </div>
          <div
            className="xs:mt-8 lg:mt-[28px] md:ml-10">
            {imagesAndLabels.map((item, index) => (
              <div key={index} className="flex ">
                <div
                  className={`flex ${item.id == 0
                    ? "flex xs:flex-col md:flex-row items-center xl:mt-[95px] lg:mt-[5px] md:mt-[-20px] xl:ml-[1rem] lg:ml-[0rem] md:ml-[-10px]    xs:mt-0 xs:ml-0 xs:w-full"
                    : item.id == 1
                      ? "flex xs:flex-col md:flex-row items-center xl:mt-[78px] lg:mt-[40px]  md:mt-[20px] xl:ml-[5rem] lg:ml-[4rem] md:ml-[2rem] xs:mt-0 xs:ml-0 xs:w-full"
                      : item.id == 2
                        ? "flex xs:flex-col md:flex-row items-center xl:mt-[70px] lg:mt-[40px] md:mt-[20px] xl:ml-[5rem] lg:ml-[4rem] md:ml-[2rem]    xs:mt-0 xs:ml-0 xs:w-full"
                        : "flex xs:flex-col md:flex-row items-center xl:mt-[50px]  lg:mt-[50px]   md:mt-[30px] xl:ml-[0rem]  lg:ml-[0rem] md:ml-[2 rem]  xs:mt-0 xs:ml-0 xs:w-full"
                    }`}
                >
                  <div>
                    <div className="border-2 border-Contessa rounded-full overflow-hidden py-3 px-3">
                      <div className="relative rounded-full w-[40px] h-[40px]">
                        <div className={`rounded-full overflow-hidden }`}>
                          <Image
                            src={item.image}
                            alt="animated logo"
                            width={40}
                            height={40}
                            className={`absolute transition-opacity duration-1000 ${item.id == 0
                              ? "w-[40px] h-[40px]"
                              : item.id == 1
                                ? "w-[40px] h-[40px]"
                                : item.id == 2
                                  ? "w-[40px] h-[40px]"
                                  : "w-[40px] h-[40px]"
                              } ${currentImageIndex === index
                                ? "opacity-0"
                                : "opacity-100"
                              }
                            `}
                          />
                          <Image
                            src={item.filledIcon}
                            alt="animated logo"
                            width={40}
                            height={40}
                            className={`absolute transition-opacity duration-1000  ${item.id == 0
                              ? "w-[40px] h-[40px]"
                              : item.id == 1
                                ? "w-[40px] h-[40px]"
                                : item.id == 2
                                  ? "w-[40px] h-[40px]"
                                  : "w-[40px] h-[40px]"
                              }
                            ${currentImageIndex === index
                                ? "opacity-100"
                                : "opacity-0"
                              }`}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="xl:ml-[3rem] lg:ml-[2rem] md:ml-[12px] xs:ml-0 xs:mt-[10px]  md:mt-0 sm:text-[18px] xl:text-[30px] xs:text-[18px] xs:pb-6 md:pb-0  text-white font-outfit font-light not-italic">
                    <LabelComponent label={item.label} />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnimatedCircle;
