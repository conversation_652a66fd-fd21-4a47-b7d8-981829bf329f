import React, { useEffect, useState } from "react";

const FinalLoading = () => {
  const [dots, setDots] = useState("");

  useEffect(() => {
    const interval = setInterval(() => {
      setDots((prev) => (prev.length < 4 ? prev + "." : ""));
    }, 2000); // Adjust timing as needed

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="fixed left-0 top-0 z-50 flex h-full w-full items-center justify-center bg-opacity-100">
      <p className="mr-2">Submitting{dots}</p>
      <div
        className="slow-spin border-white inline-block h-5 w-5 rounded-full border-4 border-solid border-current border-r-transparent align-[-0.125em]"
        role="status"
      ></div>
    </div>
  );
};

export default FinalLoading;
