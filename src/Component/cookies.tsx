import "../app/globals.css";

import React, { useEffect, useState } from "react";

import Link from "next/link";

const Cookie = () => {
  const [accepted, setAccepted] = useState(false);

  useEffect(() => {
    const consentCookie = document.cookie
      .split("; ")
      .find((row) => row.startsWith("consent="));

    if (consentCookie) {
      setAccepted(true);
    }
  }, []);

  const handleAccept = () => {
    document.cookie = "consent=true";
    setAccepted(true);
  };

  if (accepted) {
    return null;
  }

  return (
    <div className="flex justify-center items-center">
      <div className="fixed bottom-5 z-index">
        <div className="bg-black px-2 py-1 gap-x-2 flex justify-center items-center border-opacity-50 border-tosca border rounded-full font-light xs:text-[10px] sm:text-xs text-white">
          <p>By using eremotehire.com, you accept our</p>
          <Link
            href="/"
            className="bg-clip-text bg-gradient-to-r from-contessa to-tosca text-transparent"
          >
            cookie policy
          </Link>
          <button
            onClick={handleAccept}
            className="bg-CodGray rounded-full p-1"
          >
            Accept
          </button>
        </div>
      </div>
    </div>
  );
};
export default Cookie;
