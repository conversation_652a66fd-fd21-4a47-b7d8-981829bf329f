import "react-phone-input-2/lib/style.css";
import "react-phone-input-2/lib/material.css";
import "../app/globals.css";

import { AnimatePresence, motion } from "framer-motion";
import { Images, Strings } from "@/constant";
import PhoneInput, { CountryData } from "react-phone-input-2";
import React, { useEffect, useRef, useState } from "react";

import { ArrowLeftIcon } from "@heroicons/react/24/outline";
import { CloseOutlined } from "@ant-design/icons";
import FileUploader from "./FileUploader";
import Image from "next/image";
import { Line } from "rc-progress";
import Link from "next/link";
import Loading from "./Loading";
import Swal from "sweetalert2";
import axios from "axios";

export const toggleModal1 = (
  isOpen1: boolean,
  setIsOpen1: {
    (value: React.SetStateAction<boolean>): void;
    (arg0: boolean): void;
  }
) => {
  setIsOpen1(!isOpen1);
};

const Applyasanengineer = ({
  isOpen1,
  setIsOpen1,
}: {
  isOpen1: boolean;
  setIsOpen1: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const modalRef = useRef<HTMLDivElement>(null);
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [phoneNumber, setPhoneNumber] = useState("");
  const [linkedinURL, setLinkedinURL] = useState("");
  const [file, setFile] = useState<File | null>(null);
  const [rememberMe, setRememberMe] = useState(false);
  const [error, setError] = useState("");
  const [animating, setAnimating] = useState(false);
  const [activeModalPage, setActiveModalPage] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const progress = (activeModalPage / 6) * 100;

  const handleFormSubmit = async () => {
    try {
      if (!file) {
        setError("Please upload your resume.");
        return false;
      }
      if (!rememberMe) {
        setError("You must accept the Terms and Conditions to proceed.");
        return false;
      }
      setIsLoading(true);
      const formData = new FormData();
      formData.append("files", file as Blob);
      formData.append("name", name);
      formData.append("emailId", email);
      formData.append("phoneNo", phoneNumber);
      formData.append("linkedInUrl", linkedinURL);

      const config = {
        method: "post",
        maxBodyLength: Infinity,
        url: `${process.env.NEXT_PUBLIC_API_BASE_URL}applyAsEngineer/addData`,
        headers: {
          "Content-Type": "multipart/form-data",
        },
        data: formData,
      };
      console.log("first", JSON.stringify(formData));
      const response = await axios.request(config);
      if (response) {
        Swal.fire({
          toast: true,
          position: "top-end",
          showConfirmButton: false,
          timer: 3000,
          icon: "success",
          title: "The form was submitted successfully.",
          padding: "10px 20px",
          customClass: {
            popup: "swal-custom-toast",
          },
        });
        setActiveModalPage(activeModalPage + 1);
      }
    } catch (error) {
      console.error(error);
      Swal.fire({
        toast: true,
        position: "top-end",
        showConfirmButton: false,
        timer: 3000,
        icon: "error",
        title: "Something went wrong!",
        padding: "10px 20px",
        customClass: {
          popup: "swal-custom-toast",
        },
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (isOpen1) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "";
    }
  }, [isOpen1]);

  useEffect(() => {
    const rememberMeValue = localStorage?.getItem("rememberMe");
    if (rememberMeValue === "true") {
      setRememberMe(true);
    }
  }, []);

  useEffect(() => {
    if (isOpen1) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "";
    }
  }, [isOpen1]);

  const openModal = () => {
    setAnimating(true);
    setTimeout(() => {
      setIsOpen1(false);
      setAnimating(false);
    }, 500);
  };

  const handleClickOutside = (event: MouseEvent) => {
    if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
      openModal();
    }
  };

  useEffect(() => {
    if (isOpen1) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [isOpen1]);

  const validateCurrentPage = (): boolean => {
    switch (activeModalPage) {
      case 1:
        if (!name.trim()) {
          setError("Please enter your name.");
          return false;
        }
        break;
      case 2:
        if (!email.trim() || !/^\S+@\S+\.\S+$/.test(email)) {
          setError("Please enter a valid email address.");
          return false;
        }
        break;
      case 3:
        if (!phoneNumber.trim() || phoneNumber.length < 10) {
          setError("Please enter a valid phone number (minimum 10 digits).");
          return false;
        }
        break;
      case 4:
        if (!linkedinURL.trim() || !linkedinURL.startsWith("https://")) {
          setError(
            "Please enter a valid LinkedIn URL (must start with https://)."
          );
          return false;
        }
        break;
      case 5:
        if (!rememberMe) {
          setError("You must accept the Terms and Conditions to proceed.");
          return false;
        }
        break;
      default:
        break;
    }
    setError("");
    return true;
  };

  const handleNext = () => {
    if (validateCurrentPage()) {
      setActiveModalPage(activeModalPage + 1);
    }
  };

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Enter") {
        if (activeModalPage < 5) {
          handleNext();
        }
      }
    };
    window.addEventListener("keydown", handleKeyDown);
    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [activeModalPage, name, email, phoneNumber, linkedinURL, file]);

  return (
    <div>
      {(isOpen1 || animating) && (
        <div className="fixed top-0 left-0 w-full h-full flex justify-center items-end z-40 bg-gray-500 bg-opacity-[20%] backdrop-blur-sm">
          <button
            className={`fixed top-7 right-7 flex h-8 w-8 items-center justify-center rounded-full text-white transition-transform hover:scale-110 transform duration-500 ${
              isOpen1 || animating ? "opacity-100" : "opacity-0"
            }`}
            onClick={openModal}
          >
            <CloseOutlined rev={undefined} />
          </button>
          <div
            ref={modalRef}
            className={`bg-black font-outfit rounded-t-3xl w-full h-[90%] overflow-y-scroll no-scrollbar transform transition-transform duration-300 
            ${
              isOpen1 && !animating ? "slide-in" : "slide-out"
            } flex justify-center items-center p-5`}
          >
            <div className="bg-gradient-to-r from-Tosca to-contessa w-full pb-[1px] absolute top-0">
              <div className="relative flex items-center bg-black xs:h-16 md:h-20">
                {activeModalPage > 1 && (
                  <button
                    onClick={() => {
                      setActiveModalPage(activeModalPage - 1);
                      setError("");
                    }}
                    className="absolute left-7 hover:bg-[linear-gradient(to_top,_#241415,_black)] border border-tosca rounded-md flex justify-center gap-x-2 items-center h-10 w-24 text-sm text-white"
                  >
                    <ArrowLeftIcon className="w-5 h-5" />
                    Back
                  </button>
                )}
                <div className="gap-x-2 flex items-center absolute right-7 text-white">
                  <Line
                    percent={progress}
                    strokeWidth={3}
                    trailWidth={3}
                    strokeColor="#009c20"
                    trailColor="#122f17"
                    className="w-40"
                  />
                  <h1>{activeModalPage}/6</h1>
                </div>
              </div>
            </div>
            <div className="w-full xs:mx-5 md:mx-20 lg:mx-20 xl:mx-[20%] text-white">
              <AnimatePresence mode="wait">
                {activeModalPage === 1 && (
                  <motion.div
                    key="page1"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.5, ease: "easeInOut" }}
                  >
                    <h1 className="font-semibold xs:text-xl md:text-2xl lg:text-4xl">
                      {Strings.Q1}
                    </h1>
                    <div className="mt-5 bg-gradient-to-r from-Tosca to-contessa p-[1px] rounded-lg ">
                      <input
                        type="text"
                        required
                        placeholder="Your Name"
                        className="inset-0 bg-[linear-gradient(to_top,_#241415,_black)] w-full xs:h-14 lg:h-16 rounded-lg px-3 outline-none bg-black xs:text-sm lg:text-lg"
                        value={name}
                        onChange={(e) => {
                          const cleanedValue = e.target.value.replace(
                            /[0-9]/g,
                            ""
                          );
                          setName(cleanedValue);
                          if (cleanedValue.trim()) setError("");
                        }}
                      />
                    </div>
                    <div className="mt-5 bg-gradient-to-r from-Tosca to-contessa p-[0.6px]">
                      <div className="flex items-center bg-black"></div>
                    </div>
                  </motion.div>
                )}
                {activeModalPage === 2 && (
                  <motion.div
                    key="page2"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.5, ease: "easeInOut" }}
                  >
                    <h1 className="font-semibold xs:text-xl md:text-2xl lg:text-4xl">
                      {Strings.Q2}
                    </h1>
                    <div className="mt-5 bg-gradient-to-r from-Tosca to-contessa p-[1px] rounded-lg ">
                      <input
                        type="email"
                        placeholder="Your Email"
                        className="inset-0 bg-[linear-gradient(to_top,_#241415,_black)] w-full xs:h-14 lg:h-16 rounded-lg px-3 outline-none bg-black xs:text-sm lg:text-lg"
                        value={email}
                        onChange={(e) => {
                          setEmail(e.target.value);
                          if (/^\S+@\S+\.\S+$/.test(e.target.value.trim()))
                            setError("");
                        }}
                      />
                    </div>
                    <div className="mt-5 bg-gradient-to-r from-Tosca to-contessa p-[0.6px]">
                      <div className="flex items-center bg-black"></div>
                    </div>
                  </motion.div>
                )}
                {activeModalPage === 3 && (
                  <motion.div
                    key="page3"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.5, ease: "easeInOut" }}
                  >
                    <h1 className="font-semibold xs:text-xl md:text-2xl lg:text-4xl">
                      {Strings.Q3}
                    </h1>
                    <div className="mt-5 bg-gradient-to-r from-Tosca to-contessa p-[1px] rounded-lg">
                      <PhoneInput
                        country={"in"}
                        value={phoneNumber}
                        placeholder="Your Phone Number"
                        onChange={(
                          value: string,
                          data: {} | CountryData,
                          event: React.ChangeEvent<HTMLInputElement>,
                          formattedValue: string
                        ) => {
                          setPhoneNumber(value);
                          if (value.trim().length >= 10) setError("");
                        }}
                        inputClass="phoneinput inset-0 bg-[linear-gradient(to_top,_#241415,_black)]"
                        specialLabel=""
                      />
                    </div>
                    <div className="mt-5 bg-gradient-to-r from-Tosca to-contessa p-[0.6px]">
                      <div className="flex items-center bg-black"></div>
                    </div>
                  </motion.div>
                )}
                {activeModalPage === 4 && (
                  <motion.div
                    key="page4"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.5, ease: "easeInOut" }}
                  >
                    <h1 className="font-semibold xs:text-xl md:text-2xl lg:text-4xl">
                      {Strings.Q4}
                    </h1>
                    <div className="mt-5 bg-gradient-to-r from-Tosca to-contessa p-[1px] rounded-lg ">
                      <input
                        type="text"
                        placeholder="Enter LinkedIn link here"
                        className="inset-0 bg-[linear-gradient(to_top,_#241415,_black)] w-full xs:h-14 lg:h-16 rounded-lg px-3 outline-none bg-black xs:text-sm lg:text-lg"
                        value={linkedinURL}
                        onChange={(e) => {
                          setLinkedinURL(e.target.value);
                          if (e.target.value.trim().startsWith("https://"))
                            setError("");
                        }}
                      />
                    </div>
                    <div className="mt-5 bg-gradient-to-r from-Tosca to-contessa p-[0.6px] shadow-lg">
                      <div className="flex items-center bg-black"></div>
                    </div>
                  </motion.div>
                )}
                {activeModalPage === 5 && (
                  <motion.div
                    key="page5"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.5, ease: "easeInOut" }}
                  >
                    <h1 className="font-semibold xs:text-xl md:text-2xl lg:text-4xl">
                      {Strings.Q5}
                    </h1>
                    <FileUploader onFileSelect={setFile} />
                    <div className="my-5 bg-gradient-to-r from-Tosca to-contessa p-[0.6px]">
                      <div className="flex items-center bg-black"></div>
                    </div>
                    <div className="xs:text-sm md:text-base font-medium flex items-center space-x-1 text-white">
                      <input
                        type="checkbox"
                        className="form-checkbox h-3 w-3 bg-black cursor-pointer"
                        checked={rememberMe}
                        onChange={() => {
                          setRememberMe(!rememberMe);
                          setError("");
                        }}
                      />
                      <p className="text-white">{Strings.Iaccept}</p>
                      <Link href="/terms-condition">
                        <u className="text-contessa">
                          {Strings.Term_Condition}
                        </u>
                      </Link>
                    </div>
                  </motion.div>
                )}
                {activeModalPage === 6 && (
                  <motion.div
                    key="page6"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.5, ease: "easeInOut" }}
                  >
                    <div className="gap-y-4 text-center">
                      <div className="text-transparent bg-gradient-to-tr from-tosca to-contessa bg-clip-text font-semibold xs:text-2xl md:text-4xl">
                        {Strings.THANK_YOU_FOR}
                      </div>
                      <div className="text-transparent bg-gradient-to-tr from-tosca to-contessa bg-clip-text font-medium xs:text-xl md:text-2xl">
                        {Strings.THE_FORM}
                      </div>
                    </div>
                  </motion.div>
                )}
                <div className="flex justify-start">
                  {error && (
                    <p className="text-red-500 fixed font-normal mt-2 text-center">
                      {error}
                    </p>
                  )}
                </div>
              </AnimatePresence>
            </div>
            <div className="bg-gradient-to-l from-Tosca to-contessa absolute bottom-0 w-full pt-[1px]">
              <div className="flex bg-black justify-end items-center xs:h-16 md:h-20 px-7">
                {activeModalPage === 5 ? (
                  <button
                    onClick={() => handleFormSubmit()}
                    className="transition-transform hover:scale-110 transform bg-gradient-to-t from-tosca to-contessa rounded-full flex justify-center gap-2 items-center h-10 w-24 text-sm text-white "
                  >
                    {isLoading ? <Loading /> : "Submit"}
                  </button>
                ) : (
                  <>
                    {activeModalPage < 5 && (
                      <button
                        onClick={handleNext}
                        className="transition-transform hover:scale-110 transform duration-300 bg-gradient-to-t from-tosca- to-contessa- from-Contessa to-Tosca hover:bg-gradient-to-b  hover:from-Contessa  hover:to-Tosca rounded-full flex justify-center gap-2 items-center h-10 w-24 text-sm text-white"
                      >
                        Next
                      </button>
                    )}
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Applyasanengineer;
