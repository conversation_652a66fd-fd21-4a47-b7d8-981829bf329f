import { Images, Social_link, Strings } from "@/constant";

import { Image } from "antd";
import { LabelComponent } from "./label";
import Link from "next/link";
import React from "react";
import { useRouter } from "next/navigation";

export const Footer = () => {
  const router = useRouter();
  return (
    <div className=" bg-red-700- xs:px-[2rem] md:px-[4rem] lg:px-[9rem] xl:mt-[100px]- lg:mt-[80px]- md:mt-[100px]- xs:text-white">
      <div className="xs:flex-col md:flex-row justify-between flex md:items-center- ">
        <div className="flex flex-col text-left  ">
          <div className="ml-[-26px]">
            {/* <Image
              preview={false}
              src={Images.logo}
              width={200}
              height={40}
              alt="/"
              className="bg-red-400- "
            /> */}
            <button type="button" onClick={() => router.push("/")}>
              <Image
                src={Images.logo}
                width={200}
                height={40}
                alt="/"
                preview={false}
              />
            </button>
          </div>

          <div>
            <div>
              <Link href={`tel:${Strings.PHONE_NO}`}>
                <LabelComponent
                  label={Strings.PHONE_NO}
                  className={
                    "font-outfit font-light text-xl hover:opacity-[0.6] "
                  }
                />
              </Link>
            </div>
            <div className="mt-2">
              <a href="mailto:<EMAIL>">
                <LabelComponent
                  label={Strings.SUPPORT_EMAIL}
                  className={
                    "font-outfit text-xl font-light hover:opacity-[0.6]"
                  }
                />
              </a>
            </div>
            <div className="mt-5 ">
              <a
                href="https://maps.app.goo.gl/7b7LxP4EJcFvU4ZR8?g_st=ic"
                className="hover:opacity-[0.6]"
              >
                <LabelComponent
                  label={Strings.ADDRESS}
                  className={"font-outfit text-[16px] font-light  w-full"}
                />
                <LabelComponent
                  label={Strings.ADDRESS_TWO}
                  className={"font-outfit text-[16px] font-light   w-full"}
                />
                <LabelComponent
                  label={Strings.ADDRESS_T}
                  className={"font-outfit text-[16px] font-light  w-full"}
                />
              </a>
            </div>
          </div>
        </div>
        <div className="flex xs:flex-col md:flex-row  lg:w-full justify-end lg:space-x-[5rem] xlg:space-x-[25rem] md:space-x-[3rem] ">
          <div className="xs:mt-9 md:mt-0 xl:ml-[150px]">
            <LabelComponent
              label={Strings.WHY_EREMOTE}
              className={"font-outfit text-xl font-medium"}
            />
            <div className="mt-4">
              <button
                // href="/howitworks"
                onClick={() => router.push("/how-it-works")}
              >
                <LabelComponent
                  label={Strings.How_it_works}
                  className={
                    "font-outfit text-base font-medium hover:opacity-[0.6]"
                  }
                />
              </button>

              <div className="mt-2">
                <button onClick={() => router.push("/vetting-process")}>
                  <LabelComponent
                    label={Strings.VETTING_PROCESS}
                    className={
                      "font-outfit text-base my-2 font-medium hover:opacity-[0.6]"
                    }
                  />
                </button>
              </div>
              {/* <div className="mt-2">
                <button>
                  <LabelComponent
                    label={Strings.PRODUCT}
                    className={"font-outfit text-base my-2 font-medium"}
                  />
                </button>
              </div> */}
              <div className="mt-2">
                <button onClick={() => router.push("/apply-as-an-engineer")}>
                  <LabelComponent
                    label={Strings.APPLY_AS_AN_ENGINEER}
                    className={
                      "font-outfit text-base my-2 font-medium hover:opacity-[0.6]"
                    }
                  />
                </button>
              </div>
            </div>
          </div>

          {/* <div className="xs:mt-5 md:mt-0">
            <LabelComponent
              label={Strings.RESOURCE}
              className={"font-outfit text-xl font-medium"}
            />
            <div className="mt-4">
              <button>
                <LabelComponent
                  label={Strings.HELP_CENTER}
                  className={"font-outfit text-base font-medium"}
                />
              </button>

              <div className="mt-2">
                <button>
                  <LabelComponent
                    label={Strings.LEARNING_HUB}
                    className={"font-outfit text-base my-2 font-medium"}
                  />
                </button>
              </div>
              <div className="mt-2">
                <button>
                  <LabelComponent
                    label={Strings.GLOSSARY}
                    className={"font-outfit text-base my-2 font-medium"}
                  />
                </button>
              </div>
            </div>
          </div> */}

          <div className="xs:mt-5 md:mt-0">
            <LabelComponent
              label={Strings.COMPANY}
              className={"font-outfit text-xl font-medium"}
            />
            <div className="mt-4">
              <button
                //  href="/about"
                onClick={() => router.push("/about-us")}
              >
                <LabelComponent
                  label={Strings.ABOUT_US}
                  className={
                    "font-outfit text-base font-medium hover:opacity-[0.6]"
                  }
                />
              </button>

              <div className="mt-2">
                <button>
                  <LabelComponent
                    label={Strings.CAREERS}
                    className={"font-outfit text-base my-2 font-medium hover:opacity-[0.6]"}
                  />
                </button>
              </div>
              <div className="mt-2">
                <button onClick={() => router.push("/how-it-works#faq")}>
                  <LabelComponent
                    label={Strings.FAQS}
                    className={
                      "font-outfit text-base my-2 font-medium hover:opacity-[0.6]"
                    }
                  />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="mt-10 items-center flex xs:mx-2  md:mx-0 ">
        <a href={Social_link.LinkdInUrl} target="_blank">
          <Image
            src={Images.LINKDIN}
            preview={false}
            height={30}
            width={30}
            alt="/"
          />
        </a>
        <a
          href={Social_link.InstaUrl}
          target="_blank"
          className="ml-[10px] text-Contessa"
        >
          <Image
            src={Images.erh_insta}
            preview={false}
            alt="/"
            height={27}
            width={27}
          />
        </a>
        <a
          href={Social_link.TwitterUrl}
          target="_blank"
          className="ml-[10px] text-Contessa"
        >
          <Image
            src={Images.Twitter}
            preview={false}
            alt="/"
            height={27}
            width={27}
          />
        </a>
      </div>
      <div className="h-[1px]  md:mx-0 md:w-full bg-gradient-to-tl from-contessa to-tosca rounded-xl mt-3 "></div>
      <div className="flex md:justify-end mt-2 mb-4">
        <LabelComponent
          label={`Copyright © ${new Date().getFullYear()} Inc. All rights reserved.`}
          className={
            "font-outfit md:text-xs lg:text-base xs:text-[7px]  font-medium"
          }
        />
        <Link href="/privacy-policy">
          <LabelComponent
            label={Strings.PRIVACYPOLICY}
            className={
              " border-l pl-[10px] pr-[6px] ml-[10px] cursor-pointer  font-outfit md:text-xs lg:text-base xs:text-[7px]  font-medium hover:underline"
            }
          />
        </Link>
        <Link href="/terms-condition">
          <LabelComponent
            label={Strings.Term_Condition}
            className={
              " border-l px-[10px] mx-[5px] cursor-pointer  font-outfit md:text-xs lg:text-base xs:text-[7px]  font-medium hover:underline"
            }
          />
        </Link>
      </div>
    </div>
  );
};
