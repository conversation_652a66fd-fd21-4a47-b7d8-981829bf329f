import React, { useEffect, useState } from "react";

interface NumberCounterProps {
  end: number;
}

const NumberCounter: React.FC<NumberCounterProps> = ({ end }) => {
  const [count, setCount] = useState<number>(0);

  useEffect(() => {
    const interval = setInterval(() => {
      if (count < end) {
        setCount((prevCount) => prevCount + 1);
      } else {
        clearInterval(interval);
      }
    }, 100); // Update counter every 100 milliseconds

    return () => clearInterval(interval); // Clean up interval on component unmount
  }, [count, end]);

  return (
    <span className="text-center text-white font-open font-extrabold xs:text-[50px] md:text-[75px] sm:text-[65px] lg:text-[70px] xl:text-[100px]">
      {count}
    </span>
  );
};

export default NumberCounter;
