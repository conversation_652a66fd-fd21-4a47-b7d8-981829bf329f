// AccordionWrapper.js

import React, { useEffect, useState } from "react";

import FaqAccordion from "./FaqAccordion";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";

interface AccordionWrapperProps {
  data: any;
}

const AccordionWrapper: React.FC<AccordionWrapperProps> = ({ data }) => {
  const [isAnimated, setIsAnimated] = useState(false);
  const [isSmallScreen, setIsSmallScreen] = useState(false);

  const { ref, inView } = useInView({
    triggerOnce: true,
  });

  React.useEffect(() => {
    if (inView) {
      setIsAnimated(true);
    }
  }, [inView]);

  const checkScreenSize = () => {
    setIsSmallScreen(window.innerWidth <= 768);
  };

  useEffect(() => {
    checkScreenSize();

    window.addEventListener("resize", checkScreenSize);

    return () => window.removeEventListener("resize", checkScreenSize);
  }, []);

  const storyTitle2 = {
    hideBottom: {
      opacity: 0,

      y: isSmallScreen ? 100 : 200,
    },
    showBottom: {
      opacity: 1,
      y: 0,
      transition: {
        ease: "easeInOut",
        duration: 1,
      },
    },
    exit: {
      opacity: 0,
      y: isSmallScreen ? -100 : -200,
      transition: {
        ease: "easeInOut",
        duration: 0.5,
      },
    },
  };
  return (
    <div>
      {data?.map((currEle: any, index: number) => {
        const { id } = currEle;
        return (
          <motion.div
            key={id}
            initial="hideBottom"
            whileInView="showBottom"
            exit="exit"
            variants={storyTitle2}
            ref={ref}
            animate={isAnimated ? "showBottom" : "hideBottom"}
          >
            <FaqAccordion key={id} {...currEle} defaultOpen={false} />
          </motion.div>
        );
      })}
    </div>
  );
};

export default AccordionWrapper;
