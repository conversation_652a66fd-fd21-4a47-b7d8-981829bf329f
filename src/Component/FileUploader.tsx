import React, { useRef, useState } from "react";

interface FileUploaderProps {
  onFileSelect: (file: File | null) => void;
}

const FileUploader: React.FC<FileUploaderProps> = ({ onFileSelect }) => {
  const [file, setFile] = useState<File | null>(null);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (selectedFile: File) => {
    const allowedTypes = [
      "application/pdf",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    ];
    const maxSize = 10 * 1024 * 1024;

    if (!allowedTypes.includes(selectedFile.type)) {
      setError("Only PDF or DOC/DOCX files are allowed.");
      setFile(null);
      return;
    }

    if (selectedFile.size > maxSize) {
      setError("File size must be less than 10MB.");
      setFile(null);
      onFileSelect(null);
      return;
    }

    setError(null);
    setFile(selectedFile);
    onFileSelect(selectedFile);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      handleFileChange(e.dataTransfer.files[0]);
      e.dataTransfer.clearData();
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFileChange(e.target.files[0]);
    }
  };

  const handleDoubleClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  return (
    <div className="mt-5 w-full space-y-2 text-center">
      <div
        onDrop={handleDrop}
        onDragOver={(e) => e.preventDefault()}
        onDoubleClick={handleDoubleClick}
        className={`w-full h-40 flex items-center justify-center border-2 border-dashed rounded-lg ${
          error ? "border-red-500" : "border-tosca"
        } inset-0 bg-[linear-gradient(to_top,_#241415,_black)] text-center text-white p-4 cursor-pointer`}
      >
        <p className="text-sm font-normal">
          Drag and drop your file here, or{" "}
          <label
            htmlFor="file-upload"
            className="text-contessa underline cursor-pointer"
          >
            browse
          </label>
          <br />
          Max file size 10mb
        </p>
        <input
          id="file-upload"
          type="file"
          accept=".pdf,.doc,.docx"
          onChange={handleInputChange}
          className="hidden"
          ref={fileInputRef}
        />
      </div>
      {file && (
        <p className="text-sm text-green-400">
          Uploaded: {file.name} ({(file.size / 1024 / 1024).toFixed(2)} MB)
        </p>
      )}
      {error && <p className="text-sm text-red-500">{error}</p>}
    </div>
  );
};

export default FileUploader;
