import "../app/globals.css";

import React from "react";

interface SimplePricingProps {
  title: string;
  text: string;
  btntext: string;
  href?: string;
  onBtnClick?: () => void;
}
const SimplePricing: React.FC<SimplePricingProps> = ({
  title,
  text,
  btntext,
  href,
  onBtnClick,
}) => {
  return (
    <div>
      <div className="w-[340px] h-[420px] bg-gradient-to-b from-orange-200 to-Tosca p-[2px] rounded-2xl">
        <div className="relative bg-black h-full w-full rounded-2xl text-center flex justify-center items-center overflow-hidden group">
          <div className="absolute z-10 flex justify-center items-end -top-52 bg-gradient-to-l from-contessa to-Tosca rounded-full h-[380px] w-[380px] transition-all duration-1000 ease-[cubic-bezier(0.63, 0.15, 0.03, 1.12)] group-hover:h-[900px]"></div>
          <h1 className="absolute top-10 font-semibold text-5xl text-white z-20">
            {title}
          </h1>
          <p className="z-20 mt-7 mx-7 group-hover:mx-10 transition-transform duration-500 transform group-hover:scale-125 font-extralight text-base text-white">
            {text}
          </p>
          {href ? (
            <div className="flex justify-center items-center absolute z-20 bottom-5 bg-gradient-to-l from-contessa to-Tosca group-hover:bg-[linear-gradient(to_top,_#241415,_black)] group-hover:border border-tosca transition-transform hover:scale-110 transform duration-700 px-4 h-12 rounded-full">
              <a href={href} target="_blank" rel="noopener noreferrer">
                <h1 className="font-light xs:text-xl lg:text-2xl text-white">
                  {btntext}
                </h1>
              </a>
            </div>
          ) : (
            <button
              onClick={onBtnClick}
              className="absolute text-white z-20 bottom-5 justify-center items-center bg-gradient-to-l from-contessa to-Tosca group-hover:bg-[linear-gradient(to_top,_#241415,_black)] group-hover:border border-tosca transition-transform hover:scale-110 transform duration-700 px-4 h-12 rounded-full inline-block"
            >
              <h1 className="font-light xs:text-xl lg:text-2xl">{btntext}</h1>
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default SimplePricing;
