import Image from "next/image";
import React from "react";

interface GptVettingCardProps {
  image: string;
  text: string;
}

const GptVettingCard: React.FC<GptVettingCardProps> = ({ image, text }) => {
  return (
    <div className="h-[337px] w-full md:w-[490px] xs:w-[290px] rounded-lg bg-gradient-to-l from-[#481e1f] to-[#6b443a] p-[2px]">
      <div className="h-full w-full rounded-lg bg-black p-7 md:px-14 lg:px-24 flex flex-col justify-center items-center">
        <div className="relative flex justify-center items-center bg-slate-300-">
          <div className="relative">
            <Image
              src={`/images/Ellipse-467.svg`}
              alt="/"
              width={500}
              height={500}
              className="absolute top-6"
            />
            <Image
              src={`/images/bg-line.svg`}
              alt="/"
              width={500}
              height={500}
              className=""
            />
          </div>
          <div className="absolute border border-tosca border-opacity-50 h-28 w-28 rounded-full flex justify-center items-center">
            <div className="border border-tosca border-opacity-50 h-20 w-20 rounded-full bg-[linear-gradient(to_top,_#241415,_black)] flex justify-center items-center">
              <Image
                src={image}
                alt="/"
                width={500}
                height={500}
                className="h-10 w-10"
              />
            </div>
          </div>
        </div>
        <h1 className="text-white font-normal text-lg  text-center">{text}</h1>
      </div>
    </div>
  );
};

export default GptVettingCard;
