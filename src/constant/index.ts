export const Images = {
  laptop_men: "/images/U_men.png",
  ellipse_men: "/images/Ellipse 41.png",
  ellipse_menn: "/images/Ellipse 21.png",
  ellipse_mennn: "/images/Ellipse 22.png",
  ellipse_men1: "/images/Ellipse 41 (1).png",
  ellipse_men2: "/images/Ellipse 41 (2).png",
  ellipse_men3: "/images/Ellipse 41 (3).png",
  location: "/images/location.svg",
  blue_tick: "/images/verified 1.svg",
  <PERSON>_<PERSON>: "/images/Men.png",
  logo: "/images/ERH_Vector_Logo_New.png",
  Logo: "/images/ERH_Light_Logo.svg",
  LINKDIN: "/images/linkdin.svg",
  bg_image: "/images/bgg.svg",
  shadow: "/images/light.svg",
  vector: "/images/Vector.svg",
  plus: "images/plus.svg",
  pluss: "images/pluss.svg",
  Photo_10: "/images/Photo 10.png",
  Photo_11: "/images/Photo 11.png",
  Photo_12: "/images/Photo 12.png",
  Photo_13: "/images/Photo 13.png",
  Photo_14: "/images/Photo 14.png",
  Photo_15: "/images/Photo 15.png",
  Photo_16: "/images/Photo 16.png",
  ReactSvg: "images/React.svg",
  EXPRESS: 'images/express.svg',
  WEB3: 'images/web3.svg',
  JAVA: 'images/java.svg',
  JS: "images/JS.svg",
  Figma: "images/Figma.svg",
  illustrator: "images/strator 2.svg",
  photoshop: "images/photoshop.svg",
  img1: "images/im 38 .svg",
  gruop: "/images/Group 37.svg",
  componen50: "images/Component 50.svg",
  component: "images/Component 48.svg",
  component51: "images/Component 5.svg",
  componenttwo: "images/Component 49.svg",
  property: "images/Property1.svg",
  propertyOne: "images/Property 10.svg",
  propertytwo: "/images/Property 2.webp",
  propertyThree: "images/Property 3.svg",
  vectr: "images/Vector1.svg",
  img29: "images/images 29.svg",
  circle: "images/Core circle.svg",
  Shadow: "images/SShadow.svg",
  ShadowTwo: "images/Shadow2.svg",
  line: "images/line.svg",
  elipse: "/images/Ellipse 51.png",

  countbg: "/images/countbg.svg",
  user: "/images/MEN_USER.svg",
  user_two: "/images/Ellipse 41 (5).png",
  user_three: "/images/Ellipse 41 (6).png",
  user_four: "/images/Ellipse 41 (5).png",
  user_five: "/images/Ellipse 41 (8).png",
  user_six: "/images/Ellipse 41 (9).png",
  user_seven: "/images/Ellipse 41 (10).png",
  user_eight: "/images/Ellipse 41 (11).png",
  Facebook: "/images/Facebook.png",
  Instaa: "/images/Instaa.png",
  erh_insta: "/images/ERH_Vector_Instagram.svg",
  Twitter: "/images/ERH_Vector_Twitter.svg",
  group_pic: "/images/group_pic.svg",
  group_cam: "/images/group_cam.svg",
  CREDIT: "/images/credit-card.svg",
  FILL_CREDIT: "/images/creditcard2.svg",
  THREE_DOT: "/images/threedot.svg",
  THREE_DOTT: "/images/threedott.svg",
  ZOOM: "/images/zoom.svg",
  ZOOM_fill: "/images/zomm.svg",
  ICON: "/images/iconn.svg",
  ICON_fill: "/images/iconnn.svg",
  laptop: "images/laptop.svg",
  pause: "images/pause.svg",
  Next1: "images/Next1.svg",

  veify: "/images/verify.svg",
  veify_fill: "/images/verify_fill.svg",
  dollor: "/images/dollor.svg",
  dollor_fill: "/images/dollor_fill.svg",
  bussiness: "/images/bussiness.svg",
  bussiness_fill: "/images/bussiness_fill.svg",
  Cardbg: "/images/Cardbg.svg",
  Play_icon: "images/Play_icon.svg",
  videomp4: "images/videomp4.mp4",
  START_BTN: "images/Btn.svg",
  ARROW_GROUP: "/images/Vector 8.svg",
  ARROW_GROUP_Two: "/images/Line 37.svg",
  VECTOR3: "/images/Vector3.svg",
  Setting: "/images/Setting.svg",
  Setting_fill: "/images/Setting_fill.svg",
  Desktop: "/images/Desktop.svg",
  Desktop_fill: "/images/Desktop_fill.svg",
  Busines: "/images/Busines.svg",
  Busines_fill: "/images/Busines_fill.svg",
  Msg: "/images/msg.svg",
  Msg_fill: "/images/msg_fill.svg",
  Brain: "/images/Brain.svg",
  Brain_fill: "/images/Brain_fill.svg",
  USER: "/images/user.svg",
  USER_fill: "/images/user_fill.svg",
  Groupimg: "/images/groupimg 2.webp",
  Aws: "/images/Aws.png",
  Python: "/images/Python.png",
  Typescript: "/images/Typescript.png",
  checkIcon: "/images/check.png",
  javascrit: "images/javascript.svg",
  Reactnative: "/images/react-native.png",
  Blockchain: "/images/blockchain.png",
  Nextjs: "/images/nextjs.svg",
  Defaultskills: "/images/defaultSkill.svg",
  google: "/images/google.png",
  Insta: "/images/Insta.png",
  linkedin: "/images/linkedin.png",
  twitter: "/images/twitter.png",
  clutch: "/images/clutch.png",
  atom: "/images/atom.png",
  PAVAN: "images/Pavan.png",
  SAGAR: "images/sagar.webp",
  PANDYA: "images/pp.png",
  DHARMIT: "images/Dharmit.png",
  HENISH: "images/Henish.png",
  NIRDOSH: "images/nirdosh.png",
  MIHIR: "images/mihir.png",
  AVATAR: "/images/Awtar.png",
  CHOOSE: "/images/choose.webp",
  LINE_38: "/images/Line 38.png",
  MATCH_DEVELOPER: "/images/MD.png",
  TEAM_WORK: "/images/team-work.webp",
  TELL_US: "/images/Tell-us.png",
  DOUBLE_COMMA: "/images/Double.svg",
  Checkicon: "/images/checkicon.svg",
  Enter: "/images/enter.svg",
  menu: "/images/menu.png",
};

export const Skills = {
  AWS: "/ProgrammingSkills/aws.svg",
  AWSWHITE: "/ProgrammingSkills/awswhite.svg",
  ATOM: "/ProgrammingSkills/atom.png",
  BLOCKCHAIN: "/ProgrammingSkills/blockchain.png",
  REACTNATIVE: "/ProgrammingSkills/react-native.svg",
  PYTHON: "/ProgrammingSkills/python.svg",
  JAVASCRIPT: "/ProgrammingSkills/javascript.svg",
  FIGMA: "/ProgrammingSkills/figma.svg",
  REACTJS: "/ProgrammingSkills/reactjs.svg",
  REACT: "/ProgrammingSkills/react.svg",
  NODEJS: "/ProgrammingSkills/node-js.svg",
  NESTJS: "/ProgrammingSkills/nestjs.svg",
  NEXTJS: "/ProgrammingSkills/nextjs.svg",
  TYPESCRIPT: "/ProgrammingSkills/typescript.svg",
  JAVA: "/ProgrammingSkills/java.svg",
  HTML: "/ProgrammingSkills/html5.svg",
  CSS: "/ProgrammingSkills/css3.svg",
  ANDROID: "/ProgrammingSkills/android-os.svg",
  ANGULARJS: "/ProgrammingSkills/angularjs.svg",
  IOS: "/ProgrammingSkills/ios.svg",
  SWIFT: "/ProgrammingSkills/swift.svg",
  TAILWINDCSS: "/ProgrammingSkills/tailwind-css.svg",
  VUE: "/ProgrammingSkills/vue.svg",
  UNITY: "/ProgrammingSkills/unity.svg",
  DEFAULTSKILL: "/ProgrammingSkills/defaultskill.svg",
};

export const Strings = {
  How_it_works: "How it Works",
  How_We_Hire: "How we Hire",
  Pricing: "Pricing",
  Find_a_Developer_job: "Find Developer Job",
  Hire_a_Remote_job: "Hire Remote Developer",
  Log_In: "Log In",
  PHONE_NO: "+971 *********",
  SUPPORT_EMAIL: "<EMAIL>",
  WHY_EREMOTE: "Why eRemoteHire ?",
  HOW_IT_WORK: "How it work",
  VETTING_PROCESS: "Vetting Process",
  PRODUCT: "Products",
  APPLY_AS_AN_ENGINEER: "Apply as an Engineer",
  RESOURCE: "Resources",
  HELP_CENTER: "Help Center",
  LEARNING_HUB: "Learning Hub",
  GLOSSARY: "Glossary",
  COMPANY: "Company",
  ABOUT_US: "About us",
  CAREERS: "Careers",
  COPYRIGTH: "Copyright © 2023   Inc. All rights reserved",
  Privacy_policy: "Privacy policy",
  Term_Condition: "Terms & conditions",
  ADDRESS: "Building A1, Dubai Digital Park-",
  ADDRESS_TWO: "Dubai Silicon Oasis-",
  ADDRESS_T: "United Arab Emirates",
  ADDRESSS: "Building A1, Dubai Digital Park-",

  Hire_dedicated: "Hire Dedicated Remote Developers In The",
  Domain_you: "Domain You Need",
  Developer: "Developers",
  UI_UX: "UI/UX Desigeners",
  Project_M: "Project-Managers",
  what: "What do",
  Client: "Clients",
  Think: "think",
  About: "About Us ?",
  trust: "Trusted by:",
  Dev: "Developers available to",
  hire: "hire",
  now: "now",
  lorem:
    "Lorem Ipsum used since the 1500s is reproduced below for those interested.",
  section: "Sections Bonorum et Malorum original",
  tecnology: "Technologies",
  Our: "Our Engineers Know",
  Work: "Work from anywhere",
  Hire: "High compensation",
  Mangement: "Management dashboard",
  global: "Global compliance and benefits",
  developers: "Hire Developers",
  Become: "  Become a part of our",
  grow: "growing",
  community: "Community",
  our: "Our Core",
  Comptency: "Competency",
  DEVELOPER: "Developer",
  ENGINEERS: "Engineers",
  COUNTRIES: "Countries",
  CUPS_OF_COFFEE: "Cups of coffee",
  MEET_THE_FOUNDER: "Meet The Founder",
  DHAEMENDRA_VALIYA: "Dharmendra Valiya",
  FOUNDER: "Founder/CEO",
  CS_AT_UC: "CS at UC Berkeley",
  FOUNDER_AT: "Founder at eRemoteHire",
  OUR_INCREDIBLE_TEAM: "Our Incredible team",
  CEO_FOUNDER: "CEO & Founder",
  AND_MORE: "and more!",
  HIRE_MANAGE: "Hire & Manage top 1% engineers easily",
  GET_A_FULLY_REMOTE:
    "Get a fully remote, top 1% engineer in your team less than 48 hours",
  HIRE_A_TOP_ENGINEER: "Hire a Top Engineer",
  SIMPLE_PRICING: "Simple Pricing",
  TALENT: "Talent",
  COR: "COR",
  BOOK_A_DEMO: "Book a Demo",
  DEFAULT_STATE: " Default state of work is remote",
  FAQS: "FAQs",
  PICK: "Pick a top 1% engineer",
  INTERVIEW: "Interview pre-vetted engineers",
  GET: "Get recommendations ",
  TELL: "Tell us your needs  ",
  ACCESS_TO_SILICON: "Access to Silicon Valley companies",
  VETTED_ONCE: "Vetted once, certified for life",
  COMENTITIVE: "Competitive & stable income",
  FIXED_SALARY: "Fixed Salary",
  WHY_JOIN: "Why the best engineers join ",
  EREMOTE: "eremotehire",
  OUR_ROBUST_VETTING_PROCESS: "Our Robust Vetting Process",
  LOOM_VIDEO: "Loom Video",
  LOOM_DESCRIPTION:
    "First, the engineers apply online by attaching a 3 minute loom video of themselves going through their resume. Here we can quickly do most of the filtering by assessing experience, resume, and communication.",
  GPT_VETTING: "GPT-vetting",
  GPT_DECRIPTION:
    "A GPT-4 powered test deeply pre-screens the technical knowledge of all candidates.",
  TECHNICAL_DECRIPTION:
    "We then conduct 1-2 technical rounds by senior engineers in our core team.",
  TECHNICAL: "1-2 Technical interviews",
  CASUAL_INTERVIEW: "Casual Interview",
  CASUAL_DESCRIPTION:
    "After the technicals, we conduct a casual interview to deeply assess their soft skills: communication, passion, and attitude.",
  TRAINING: "Training",
  TRAINING_DESCRIPTION:
    "After they've passed our vetting process, we take each and every engineer through our 'AI-Powered Engineer' training to teach them how to use github copilot, chatGPT, copilot labs, and more. This makes each engineer approximately 2x more efficient.",
  TOP_1_CERTIFIEDE_ENGINEER: "Top 1% Certified Engineer",
  TOP_1_CERTIFIEDE_ENGINEER_DESCRIPTION: "Top 1% Certified Engineer",
  HIRE_MANAGE_tOP_1_ENGINEERS_EASILY: "Hire & Manage top 1% engineers easily",
  HIRE_MANAGE_tOP_1_ENGINEERS_EASILY_DECRIPTION:
    "Get a fully remote, top 1% engineer in your team less than 48 hours",
  PRIVACYPOLICY: "Privacy Policy",
  LOGTN: "Login",
  EMAIL: "Email",
  PASSWORD: "Password",
  FORGOT_PASSWORD: "Forgot password",
  ENTER_YOUR_EMAIL:
    "Enter your email address and we'll send you a link to reset your password.",
  YOUR_EMAIL: "Your email",
  RESET_PASSWORD: "Reset Password",
  NEW_PASSWORD: "New Password",
  CONFIRM_NEW_PASSWORD: "Confirm New Password",

  title: "CEO/Founder",
  title1: "Product Manager",
  BACKEND: "Back-End-Developer",
  FRONTEND: "Front-End-Developer",
  ENGG: "Software Engineer",
  TESTER: "QA Tester",
  UIUX: "UI-UX-Developer",
  name: "Mihir Mistry",
  name1: "Nirdosh Patil",
  name2: "Henish Patel",
  name3: "Sagar Panchal",
  name4: "Parth Pandya",
  name5: "Dharmit Hapani",
  name6: "Pavan Dubey",
  HOWITWORKP:
    " Compliance, global payroll, and benefits for your team for a fixed fee of $490/month per team member",
  HOWITWOEKPP:
    "With today  technology, a location constraint is completely unnecessary. We’re building tools to help<br></br> the future of remote work.",
  HOW_IT_WORKS_DESC:
    "With today's technology, a location constraint is completely unnecessary. We’re building tools to help \nthe future of remote work.",
  HOW_IT_WORKS_DESC2:
    "The engineer's hourly rate is used as reference to calculate the total fixed monthly pay (in other words, their salary). This is done through a stripe subscription and there are no additional costs.",
  QUS_1: " 1. What type of hire do you need?*",
  QUS_1_SUB: ` We offer full time (40 hours/week) and part time (20
    hours/week)`,
  FULL_TIME: "Full Time",
  PART_TIME: "Part Time",
  PLEASE_SELECT_AN_OPTION: "Please select an option.",
  QUS_2: "2. What skills should they have? (optional)",
  QUS_2_SUB: "If you don't know, that's okay. Just press next to skip.",
  CHOOSE_AS_MANY_AS_YOU_LIKE: "Choose as many as you like",
  ADD: "Add",
  QUS_3: "3. How many software engineers are you looking to hire?*",
  QUS_3_SUB: "You can start with 1 or 10. We have the talent ready!",
  QUS_4: "4. What’s your name?*",
  QUS_4_SUB: "Nice to meet you!",
  PLEASE_ENTER_YOUR_NAME: "Please enter your name.",
  QUS_5: " 5. What’s your last name?*",
  QUS_6: "5. What is your company email?*",
  PLEASE_ADD_YOUR_COMPANY_EMAIL: "Please add your company email",
  QUS_8: "7. Anything else you want to tell us?",
  FELL_FREE_TO_TELL:
    "Feel free to tell us about your company or anything else here",
  QUS_7: " 6. How many employees in your company?*",
  WE_TAILOR_OUR:
    "We tailor our solutions to fit your company’s specific needs.",
  SHIFT_ENTER: "Shift ⇧ + Enter ↵ to make a line break",
  QUS_9: " 8. Where did you find us?*",
  WE_APPRECIATE_IT: "We appreciate it!",
  SUBMIT: "Submit",
  THANK_YOU_FOR: "Thank you",
  THE_FORM: "The form was submitted successfully.",
  Q1: "1. What’s your name?*",
  NEXT: "Next",
  Q2: "2. What is your email?*",
  Q3: "3. What’s your phone number? *",
  Q4: "4. What is your personal LinkedIn URL?*",
  INVALID: "Invalid LinkedIn URL",
  Q5: "5. Upload your resume*",
  Iaccept: "I accept",
  Remove: "Remove",
  DRAG: "Drag & Drop files or",
  Selectfiles: "Select files ",
  toupload: "to upload",
  SUCCESS: "Success Stories",
  TEL: "Tell us about your needs",
  GETT: "Get match perfect developer",
  BEING: "Being working together",
  CHOOSE: "Choose",
  FOUR: "Four Step To Your Perfect",
  HIRE: "Hire",
  A: "A",
  B: "B",
  C: "C",
  forgot_password: "Forgot Password?",
  if_you_dont_have:
    " if you don’t have an account, check your email for an invite",
  Are_you_a_client: "Are you a client?",
  Login_here: "Login here",
  Password_must: " Password must be at least 6 characters",
  Email_is_invalid: "Email is invalid",
  Email_is_Required: " Email is Required",
  Back_to_login: "Back to login",
  PASSWORD_MUST_BE: "Password must be at least 6 characters",
  PASSWORDS_DO_NOT_MATCH: "Passwords do not match. Please try again.",
  AT_EREMOTEHIRE:
    " At eRemoteHire, we help companies hire the best remote software engineers easily. When you hire with us, you do not have to worry about finding top 1% talent, international employment laws, or benefits.",
  WE_ALSO_HAVE:
    "  We also have a 1-week free trial per engineer. We are creating the best tools for sourcing and vetting the top 1% talent globally, using AI.",
  WORK_IS_NOW:
    "Work is now remote 1st, companies will be at a significant disadvantage if they limit their engineering team to a commuting radius around a specific location. The best engineers live everywhere!",
  TALENT_TXT:
    "The rate you see on the candidate profiles is the rate you pay. No extra fees. The average rate for an engineer is $32/hour.",
  eRemoteHire: " eRemoteHire's",
  Note: "Note: please do not refresh the page or you'll lose the data.",
  AI_INTERVIEWER_Q1:
    "Hello! I am eRemoteHire's AI interviewer. Welcome, I'm excited to get to know you. Please introduce your self?",
  AI_INTERVIEWER_REPORT_TEXT:
    "The candidate has provided a correct and efficient solution to the problem. The function processes each operation in the order they appear, correctly handling both 'type' and 'delete' operations. The use of string slicing for delete operations is appropriate and ensures that no errors occur when attempting to delete more characters than available. The solution also handles edge cases, such as an empty input array, by returning an empty string. The code is clean, follows good coding standards, and is easy to understand. Overall, the candidate demonstrated a strong understanding of the problem and provided an optimal solution.",
  AI_INTERVIEWER_REPORT_TEXT2:
    "Create a simple Next.js component called Counter that displays a number. It should have two buttons, 'Increment' and 'Decrement', which increase and decrease the counter by 1. Ensure this is a client-side component.",
  RUN: `Click "Run" to see the output here`,
};
export const Social_link = {
  Instagram_Login: "https://www.instagram.com/accounts/login",
  Facbook_Login: "https://www.facebook.com/.",
  LinkdInUrl: "https://www.linkedin.com/company/eremotehire/",
  InstaUrl:
    "https://instagram.com/eremotehire?igshid=YzAwZjE1ZTI0Zg%3D%3D&utm_source=qr",
  TwitterUrl: "https://twitter.com/eRemoteHire",
};

export const LANGUAGE_VERSIONS = {
  javascript: "18.15.0",
  typescript: "5.0.3",
  python: "3.10.0",
  java: "15.0.2",
  csharp: "6.12.0",
  php: "8.2.3",
};

export const CODE_SNIPPETS = {
  javascript: `\nfunction greet(name) {\n\tconsole.log("Hello, " + name + "!");\n}\n\ngreet("Alex");\n`,
  typescript: `\ntype Params = {\n\tname: string;\n}\n\nfunction greet(data: Params) {\n\tconsole.log("Hello, " + data.name + "!");\n}\n\ngreet({ name: "Alex" });\n`,
  python: `\ndef greet(name):\n\tprint("Hello, " + name + "!")\n\ngreet("Alex")\n`,
  java: `\npublic class HelloWorld {\n\tpublic static void main(String[] args) {\n\t\tSystem.out.println("Hello World");\n\t}\n}\n`,
  csharp:
    'using System;\n\nnamespace HelloWorld\n{\n\tclass Hello { \n\t\tstatic void Main(string[] args) {\n\t\t\tConsole.WriteLine("Hello World in C#");\n\t\t}\n\t}\n}\n',
  php: "<?php\n\n$name = 'Alex';\necho $name;\n",
};
export type Language = keyof typeof CODE_SNIPPETS;
