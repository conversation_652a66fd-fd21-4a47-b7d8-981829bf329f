{"resources": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "img": "/images/Double.svg", "Post": "Director at Finnowski", "Author": " eRemoteHire web developer has been amazing! He's been with us for more than a year now, so I can confidently say that they've been fueling our growth in the long term.  ", "imageUrl": "/images/DhruvSoni.png"}, {"title": "<PERSON><PERSON><PERSON>", "img": "/images/Double.svg", "Post": "CEO at Propertize", "Author": "eRemoteHire has been a secret weapon for us in scaling our business. I almost want to leave a bad review to keep others away so that we can keep scourging the talent pool with limited competition.  ", "imageUrl": "/images/AnkitJoshi.png"}], "resource": [{"name": "<PERSON><PERSON>", "title": " Senior Back end <PERSON><PERSON><PERSON>", "subTitle": "<PERSON><PERSON> is a web developer focused on modern PHP and JS , always striving for new technologies.When he wears his back-end hat ,he's focused on performant and DRY code,working on API servers or consumers. ", "imageUrl": "/images/Image 20.png"}, {"name": "<PERSON><PERSON>", "title": "Front-end-developer", "subTitle": "<PERSON><PERSON> is a French front-end-developer who has mostly worked with JavaScript, TypeScript, Node.js, React, and React Native", "imageUrl": "/images/image 2.png"}, {"name": "Part<PERSON> Pandya", "title": "QA TESTER", "subTitle": "<PERSON><PERSON> is a user experience designer and strategist with 12+ years of experience working for companies like Deutsche Bank, Philips, Vodafone, KLM, Intel, Pernod Ricard, Asics, and Toyota.", "imageUrl": "/images/Image 26.png"}, {"name": "<PERSON><PERSON><PERSON>", "title": "Data-end-Developer", "subTitle": " <PERSON><PERSON><PERSON> is a senior UI/UX designer with ten years of professional experience specializing in designing applications for startups. <PERSON> believes that design is not about deliverables and beautiful pixels but solving problems and achieving business and user goals.", "imageUrl": "/images/Image 27.png"}, {"name": "<PERSON><PERSON>", "title": "UI/UX Developer", "subTitle": "<PERSON><PERSON> is a UI/UX specialist with more than ten years of industry experience who focuses on usability, user experience, and user research in his designs. ", "imageUrl": "/images/Image 28.png"}], "imgeData": [{"src": "/images/dashboard.png", "Num": "/images/01.png", "Title": "Tell us about your needs"}, {"src": "/images/fourone.webp", "Num": "/images/02.png", "Title": "Get match perfect developer"}, {"src": "/images/three.webp", "Num": "/images/03.png", "Title": "Being Working Togather"}, {"src": "/images/four.webp", "Num": "/images/04.png", "Title": "<PERSON><PERSON>"}], "logo": [{"src": "/images/photo 1.svg"}, {"src": "/images/photo 18.svg"}, {"src": "/images/photo 19.svg"}, {"src": "/images/photo 20.svg"}, {"src": "/images/photo 21.svg"}, {"src": "/images/photo 6 .svg"}], "imgeDataSecond": [{"src": "/images/Dharmit.png", "srcone": "/images/sagar.webp", "srctwo": "/images/Pvn.webp", "paragraph": "It has been a platform for personal and professional growth. I am grateful for the opportunities, the supportive team, and the learning environment that has shaped me into a more proficient backend developer. I would highly recommend eRemoteHire to anyone looking for a company that invests in its employee's growth and fosters a culture of excellence.", "position": "Back-end-<PERSON><PERSON><PERSON>", "line": "images/Line.svg", "Title": "<PERSON><PERSON><PERSON>"}, {"src": "/images/Pvn.webp", "srcone": "/images/sagar.webp", "srctwo": "/images/Dharmit.png", "paragraph": "Working with eRemoteHire as a UI/UX developer has been an incredible journey. This company has been pivotal in refining my design skills. The projects are challenging and rewarding, providing ample opportunities for creative expression. eremotehire an ideal workplace for anyone passionate about UI/UX development. I highly recommend it!", "Title": "<PERSON><PERSON>", "position": "UI/UX Developer"}, {"src": "/images/sagar.webp", "srcone": "/images/Pvn.webp", "srctwo": "/images/Dharmit.png", "paragraph": "I've had the privilege of working with eRemoteHire for quite a considerable time, and the experience has been truly exceptional. This company has been instrumental in shaping and elevating my front-end development skills.", "Title": "<PERSON><PERSON>", "position": "Front-End-Dev<PERSON>per"}], "animated": [{"src": "/images/Component 50.svg", "srcone": "/images/Property1.svg"}, {"src": "/images/Component 50.svg", "srcone": "/images/Property1.svg"}, {"src": "/images/Component 50.svg", "srcone": "/images/Property1.svg"}, {"src": "/images/Component 50.svg", "srcone": "/images/Property1.svg"}], "Question": [{"id": "0", "question": " What is eremotehire?", "answer": " We help companies hire & manage a top 1% engineering team easily."}, {"id": "1", "question": "  How does the free trial work?", "answer": " Ones you find the right engineer from our talent pool,you can try them out for a week, for free."}, {"id": "2", "question": "    Will the engineer work directly for me?", "answer": "The engineer will be direct contact with you and yourcompany. You can also add the engineer to all of you company channels and softwares however, you will not be directly hiring the developer,you will be hiring eremotehire hiring eremotehire builds a layer of abstraction for the legal compliance global pay and benefits."}, {"id": "3", "question": " How does billing work?", "answer": "The engineer hourly rate is used as reference to calculate the total fixed monthly pay (in other words, their salary). This is done through a stripe subscription and there are no additional costs."}, {"id": "4", "question": " How does billing work?", "answer": "The engineer hourly rate is used as reference to calculate the total fixed monthly pay (in other words, their salary). This is done through a stripe subscription and there are no additional costs."}, {"id": "5", "question": "How are you different?", "answer": " 1. Our secret sauce is that we get 500 applications a day & quickly source through them using gpt-vetting powered by gpt-4 and whisper. we then conduct multiple rounds of manual interviews before certifying each candidate.2. We have a 1 week free trial that allows you to test the engineer in the best way possible.3.  We have a management portal that allows you to easily manage the global HR aspects of your hire: time tracking, payroll weekly summaries, and more."}, {"id": "6", "question": " What technologies do eremotehire  developers know?", "answer": "React, Node.js, AWS, UI/UX, Web3, AI/ML, and much more."}, {"id": "7", "question": "   Is there a contract?", "answer": "All contracts are month to month with no minimum commitment."}], "Question_TWO": [{"id": "0", "question": "  Is this for long term hires?", "answer": "Yes! We only work with highly credible, well funded companies that are hiring for long term development roles."}, {"id": "1", "question": " Do I get a fixed salary?", "answer": "Yes, you get a fixed monthly salary that is paid bi-weekly. We pay you for 173 hours a month (see how that is calculated here)."}, {"id": "2", "question": " What are the benefits?", "answer": "We have your back with lots of competitive benefits like 15 days of PTO, a Coding chair, Laptop credit Healthcare insurance,Fast speed Wifi, Physical or Mental Health Costs, and Unlimited Udemy courses & books. The benefits only apply to full-time developers."}]}