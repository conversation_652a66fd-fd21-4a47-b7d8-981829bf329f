<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1080" height="1080" viewBox="0 0 1080 1080" xml:space="preserve">
<desc>Created with Fabric.js 5.2.4</desc>
<defs>
</defs>
<g transform="matrix(1 0 0 1 540 540)" id="34dca9eb-dca0-4928-92a5-3067757d2047"  >
<rect style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1; visibility: hidden;" vector-effect="non-scaling-stroke"  x="-540" y="-540" rx="0" ry="0" width="1080" height="1080" />
</g>
<g transform="matrix(1 0 0 1 540 540)" id="5ea2abc3-3081-4b48-84b5-f99ff6baf03a"  >
</g>
<g transform="matrix(1.87 0 0 1.87 540 540)"  >
<g style="" vector-effect="non-scaling-stroke"   >
		<g transform="matrix(1 0 0 1 0.01 0)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(20,112,184); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-256.01, -194.97)" d="M 256.02 0 C 363.69 0 450.98 87.29 450.98 194.97 C 450.98 302.64 363.69 389.93 256.02 389.93 C 148.33999999999997 389.93 61.04999999999998 302.64 61.04999999999998 194.97 C 61.05 87.29 148.34 0 256.02 0 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 0.02 0)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(187,222,251); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-256.02, -194.97)" d="M 246.24 88.71 L 246.24 34.03 C 235.76000000000002 43.27 226.17000000000002 52.46 217.45000000000002 61.61 C 208.82000000000002 70.67 201.03000000000003 79.7 194.09000000000003 88.71000000000001 L 246.24000000000004 88.71000000000001 z M 395.25 301.55 L 344.99 301.55 C 327.76 326.38 306.07 348.36 283.14 367.95000000000005 L 285.12 367.64000000000004 C 328.19 360.64000000000004 368.86 336.30000000000007 395.25 301.55000000000007 z M 228.96 367.93 C 206.05 348.36 184.34 326.36 167.14000000000001 301.55 L 116.99000000000001 301.55 C 143.51000000000002 336.47 183.87 360.57 227.10000000000002 367.63 L 228.96000000000004 367.93 z M 116.79 88.71 L 170.03 88.71 C 178.04 77.25 187.3 65.75 197.82 54.21999999999999 C 207.59 43.50999999999999 218.45999999999998 32.75999999999999 230.44 21.97999999999999 L 227.60999999999999 22.39999999999999 C 184.35999999999999 29.129999999999992 143.31 53.74999999999999 116.78999999999999 88.71 z M 281.75 22.129999999999995 C 303.98 42.06999999999999 324.93 64.13999999999999 341.98 88.71 L 395.14 88.71 C 367.59999999999997 52.489999999999995 326.62 28.86999999999999 281.75 22.129999999999995 z M 265.77 34.029999999999994 L 265.77 88.71 L 317.91999999999996 88.71 C 310.97999999999996 79.69999999999999 303.19999999999993 70.66999999999999 294.56999999999994 61.60999999999999 C 285.8499999999999 52.459999999999994 276.24999999999994 43.269999999999996 265.7699999999999 34.029999999999994 z M 265.77 301.54999999999995 L 265.77 357.03999999999996 C 285.76 340.43999999999994 304.75 322.03999999999996 320.78999999999996 301.54999999999995 L 265.77 301.54999999999995 z M 246.23999999999998 357.04999999999995 L 246.23999999999998 301.54999999999995 L 191.20999999999998 301.54999999999995 C 207.17 322.09 226.21999999999997 340.49999999999994 246.23999999999998 357.04999999999995 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 0 0.16)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(20,112,184); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-256, -195.13)" d="M 40.22 90.41 L 471.78 90.41 C 493.86999999999995 90.41 512 108.53999999999999 512 130.63 L 512 259.63 C 512 281.65999999999997 493.81 299.85 471.78 299.85 L 40.22 299.85 C 18.09 299.85 0 281.76 0 259.63 L 0 130.63 C 0 108.47999999999999 18.07 90.41 40.22 90.41 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 0 0.16)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-256, -195.13)" d="M 40.22 108.32 L 471.78 108.32 C 484.04999999999995 108.32 494.09 118.39999999999999 494.09 130.63 L 494.09 259.63 C 494.09 271.86 484.01 281.94 471.78 281.94 L 40.22 281.94 C 27.99 281.94 17.91 271.9 17.91 259.63 L 17.91 130.63 C 17.91 118.36 27.96 108.32 40.22 108.32 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 0 0.16)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(31,54,71); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-256, -195.13)" d="M 147.35 148.47 L 178.54999999999998 148.47 L 163.92999999999998 241.78 L 124.95999999999998 241.78 L 118.08999999999997 207.44 L 116.29999999999997 207.44 L 109.57999999999997 241.78 L 70.62 241.78 L 55.99 148.47 L 87.19 148.47 L 94.2 200.57999999999998 L 95.10000000000001 200.57999999999998 L 105.85000000000001 148.46999999999997 L 128.69 148.46999999999997 L 139.29 200.57999999999998 L 140.19 200.57999999999998 L 147.35 148.46999999999997 z M 246.86 206.55 L 217 206.55 L 217 219.48000000000002 L 253.57999999999998 219.48000000000002 L 253.57999999999998 241.78000000000003 L 187.14 241.78000000000003 L 187.14 148.47000000000003 L 252.82999999999998 148.47000000000003 L 249.1 170.78000000000003 L 217 170.78000000000003 L 217 184.90000000000003 L 246.86 184.90000000000003 L 246.86 206.55000000000004 z M 267.38 241.78 L 267.38 148.47 L 315.76 148.47 C 324.71 148.47 331.11 150.22 334.94 153.7 C 338.77 157.17999999999998 340.69 162.26 340.69 168.92999999999998 C 340.69 175.59999999999997 339.46999999999997 180.73999999999998 337.03 184.37999999999997 C 334.59 188.00999999999996 331.33 190.36999999999998 327.25 191.46999999999997 L 327.25 192.36999999999998 C 339.29 194.45999999999998 345.32 202.67 345.32 216.99999999999997 C 345.32 224.46999999999997 343.33 230.45999999999998 339.34999999999997 234.98999999999998 C 335.35999999999996 239.51999999999998 329.23999999999995 241.77999999999997 320.97999999999996 241.77999999999997 L 267.37999999999994 241.77999999999997 z M 308.89 204.61 L 295.65999999999997 204.61 L 295.65999999999997 219.84 L 308.73999999999995 219.84 C 312.91999999999996 219.84 315.00999999999993 217.3 315.00999999999993 212.22 C 315.00999999999993 207.15 312.9599999999999 204.60999999999999 308.88999999999993 204.60999999999999 z M 306.65 168.93 L 295.65999999999997 168.93 L 295.65999999999997 182.81 L 306.49999999999994 182.81 C 310.2799999999999 182.81 312.16999999999996 180.5 312.16999999999996 175.87 C 312.16999999999996 171.24 310.33 168.93 306.65 168.93 z M 423.91999999999996 181.32 L 423.91999999999996 170.03 C 420.92999999999995 169.53 416.09999999999997 169.29 409.43999999999994 169.29 C 402.7699999999999 169.29 394.4599999999999 170.32999999999998 384.49999999999994 172.42 L 380.61999999999995 151.91 C 392.75999999999993 148.12 404.50999999999993 146.23 415.85999999999996 146.23 C 425.60999999999996 146.23 432.67999999999995 146.82999999999998 437.05999999999995 148.01999999999998 C 441.43999999999994 149.21999999999997 444.66999999999996 150.65999999999997 446.75999999999993 152.35 C 451.3399999999999 156.23999999999998 453.62999999999994 162.10999999999999 453.62999999999994 169.97 C 453.62999999999994 179.92 449.49999999999994 187.09 441.23999999999995 191.47 L 441.23999999999995 192.22 C 451.09 196.3 456.0199999999999 203.71 456.0199999999999 214.46 C 456.0199999999999 219.74 455.19999999999993 224.24 453.55999999999995 227.97 C 451.90999999999997 231.7 449.84999999999997 234.59 447.35999999999996 236.63 C 444.86999999999995 238.67 441.59 240.29 437.49999999999994 241.48 C 431.42999999999995 243.17999999999998 422.8999999999999 244.01999999999998 411.8999999999999 244.01999999999998 C 400.8999999999999 244.01999999999998 389.87999999999994 242.73 378.8299999999999 240.14 L 383.00999999999993 217.08999999999997 C 392.4699999999999 219.37999999999997 400.4799999999999 220.52999999999997 407.04999999999995 220.52999999999997 C 413.62 220.52999999999997 419.23999999999995 220.22999999999996 423.91999999999996 219.62999999999997 L 423.91999999999996 208.48999999999995 L 398.23999999999995 205.94999999999996 L 398.23999999999995 184.3 L 423.91999999999996 181.32000000000002 z" stroke-linecap="round" />
</g>
</g>
</g>
</svg>