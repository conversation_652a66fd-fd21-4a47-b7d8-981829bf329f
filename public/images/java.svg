<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1080" height="1080" viewBox="0 0 1080 1080" xml:space="preserve">
<desc>Created with Fabric.js 5.2.4</desc>
<defs>
</defs>
<g transform="matrix(1 0 0 1 540 540)" id="8519403a-1537-4374-9c96-6b50ed47a903"  >
<rect style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1; visibility: hidden;" vector-effect="non-scaling-stroke"  x="-540" y="-540" rx="0" ry="0" width="1080" height="1080" />
</g>
<g transform="matrix(1 0 0 1 540 540)" id="b698ed1a-a5d7-48de-a4c6-77d13acdb3d7"  >
</g>
<g transform="matrix(1 0 0 1 540 540)"  >
<g style="" vector-effect="non-scaling-stroke"   >
		<g transform="matrix(1.48 0 0 1.48 204.5 234.23)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(231,111,0); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-290.21, -433.22)" d="M 285.104 430.945 L 283.067 430.945 L 283.067 429.805 L 288.553 429.805 L 288.553 430.945 L 286.528 430.945 L 286.528 436.633 L 285.10400000000004 436.633 L 285.10400000000004 430.945 z M 296.046 431.242 L 296.014 431.242 L 293.994 436.635 L 293.07000000000005 436.635 L 291.0640000000001 431.242 L 291.0400000000001 431.242 L 291.0400000000001 436.635 L 289.69700000000006 436.635 L 289.69700000000006 429.807 L 291.67300000000006 429.807 L 293.5330000000001 434.642 L 295.38700000000006 429.807 L 297.35600000000005 429.807 L 297.35600000000005 436.635 L 296.0450000000001 436.635 L 296.04600000000005 431.242 z" stroke-linecap="round" />
</g>
		<g transform="matrix(1.48 0 0 1.48 -9.95 16.99)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(83,130,161); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-145.21, -286.34)" d="M 102.681 291.324 C 102.681 291.324 88.503 299.569 112.771 302.35900000000004 C 142.171 305.713 157.197 305.232 189.596 299.1 C 189.596 299.1 198.114 304.44100000000003 210.01 309.067 C 137.38 340.195 45.633999999999986 307.264 102.681 291.324 M 93.806 250.704 C 93.806 250.704 77.904 262.475 102.19 264.987 C 133.596 268.22700000000003 158.398 268.492 201.315 260.228 C 201.315 260.228 207.252 266.246 216.586 269.53700000000003 C 128.77100000000002 295.21500000000003 30.962000000000018 271.562 93.80600000000001 250.70400000000004" stroke-linecap="round" />
</g>
		<g transform="matrix(1.48 0 0 1.48 61.6 -183.83)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(231,111,0); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-193.59, -150.56)" d="M 168.625 181.799 C 186.52100000000002 202.40300000000002 163.924 220.945 163.924 220.945 C 163.924 220.945 209.363 197.487 188.495 168.112 C 169.00400000000002 140.71699999999998 154.05700000000002 127.107 234.974 80.178 C 234.975 80.17699999999999 107.96099999999998 111.899 168.625 181.79899999999998" stroke-linecap="round" />
</g>
		<g transform="matrix(1.48 0 0 1.48 -0.96 9.63)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(83,130,161); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-151.29, -281.36)" d="M 264.684 321.369 C 264.684 321.369 275.17600000000004 330.01500000000004 253.12900000000002 336.70200000000006 C 211.20600000000002 349.40200000000004 78.64100000000002 353.2370000000001 41.815000000000026 337.20900000000006 C 28.577000000000027 331.44900000000007 53.40200000000003 323.45700000000005 61.21100000000003 321.7800000000001 C 69.35500000000003 320.01400000000007 74.00900000000003 320.3430000000001 74.00900000000003 320.3430000000001 C 59.28700000000003 309.9720000000001 -21.147999999999968 340.7060000000001 33.15200000000003 349.50900000000007 C 181.23600000000005 373.52400000000006 303.09600000000006 338.69500000000005 264.684 321.3690000000001 M 109.499 208.617 C 109.499 208.617 42.068 224.63299999999998 85.61999999999999 230.44899999999998 C 104.00899999999999 232.91099999999997 140.66699999999997 232.35399999999998 174.81199999999998 229.493 C 202.718 227.13899999999998 230.73999999999998 222.135 230.73999999999998 222.135 C 230.73999999999998 222.135 220.89999999999998 226.349 213.78099999999998 231.209 C 145.30599999999998 249.219 13.024999999999977 240.84 51.10699999999997 222.419 C 83.31299999999997 206.851 109.49899999999997 208.61700000000002 109.49899999999997 208.61700000000002 M 230.462 276.231 C 300.07 240.06 267.887 205.29899999999998 245.422 209.983 C 239.916 211.129 237.46099999999998 212.122 237.46099999999998 212.122 C 237.46099999999998 212.122 239.50599999999997 208.92000000000002 243.408 207.53400000000002 C 287.849 191.91000000000003 322.027 253.615 229.06199999999998 278.055 C 229.06199999999998 278.055 230.141 277.093 230.462 276.231" stroke-linecap="round" />
</g>
		<g transform="matrix(1.48 0 0 1.48 -2.04 -249.48)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(231,111,0); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-150.56, -106.17)" d="M 188.495 4.399 C 188.495 4.399 227.04500000000002 42.961 151.93200000000002 102.261 C 91.69900000000001 149.828 138.197 176.95 151.907 207.939 C 116.74900000000001 176.216 90.947 148.292 108.257 122.30199999999999 C 133.663 84.15099999999998 204.049 65.654 188.495 4.398999999999987" stroke-linecap="round" />
</g>
		<g transform="matrix(1.48 0 0 1.48 35.63 122.73)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(83,130,161); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-176.04, -357.84)" d="M 116.339 374.246 C 183.154 378.52299999999997 285.756 371.873 288.185 340.25899999999996 C 288.185 340.25899999999996 283.515 352.24299999999994 232.966 361.76199999999994 C 175.93900000000002 372.49299999999994 105.602 371.24099999999993 63.88500000000002 364.36299999999994 C 63.88700000000002 364.36099999999993 72.42600000000002 371.42999999999995 116.33900000000003 374.2459999999999" stroke-linecap="round" />
</g>
		<g transform="matrix(1.48 0 0 1.48 -11.78 292.17)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(231,111,0); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-143.98, -472.4)" d="M 105.389 495.049 C 99.086 500.51599999999996 92.429 503.585 86.455 503.585 C 77.928 503.585 73.321 498.472 73.321 490.27099999999996 C 73.321 481.4 78.258 474.914 98.06 474.914 L 105.388 474.914 L 105.38900000000001 495.049 M 122.781 514.672 L 122.781 453.93 C 122.781 438.41200000000003 113.93100000000001 428.174 92.593 428.174 C 80.136 428.174 69.224 431.25 60.355000000000004 435.173 L 62.915000000000006 445.925 C 69.89800000000001 443.362 78.93700000000001 440.976 87.809 440.976 C 100.101 440.976 105.389 445.925 105.389 456.157 L 105.389 463.835 L 99.25399999999999 463.835 C 69.389 463.835 55.91699999999999 475.428 55.91699999999999 492.828 C 55.91699999999999 507.846 64.79499999999999 516.382 81.511 516.382 C 92.256 516.382 100.27699999999999 511.94499999999994 107.77499999999999 505.453 L 109.136 514.674 L 122.78099999999999 514.672 z M 180.824 514.672 L 159.133 514.672 L 133.02700000000002 429.71200000000005 L 151.971 429.71200000000005 L 168.169 481.91100000000006 L 171.77 497.61000000000007 C 179.965 474.9120000000001 185.762 451.88400000000007 188.661 429.7120000000001 L 207.088 429.7120000000001 C 202.15 457.6880000000001 193.266 488.3960000000001 180.82399999999998 514.6720000000001 M 264.038 495.049 C 257.723 500.51599999999996 251.055 503.585 245.08 503.585 C 236.568 503.585 231.949 498.472 231.949 490.27099999999996 C 231.949 481.4 236.89600000000002 474.914 256.697 474.914 L 264.038 474.914 L 264.038 495.049 M 281.428 514.672 L 281.428 453.93 C 281.428 438.41200000000003 272.557 428.174 251.242 428.174 C 238.777 428.174 227.861 431.25 218.99599999999998 435.173 L 221.55299999999997 445.925 C 228.53799999999998 443.362 237.59399999999997 440.976 246.45899999999997 440.976 C 258.74199999999996 440.976 264.03799999999995 445.925 264.03799999999995 456.157 L 264.03799999999995 463.835 L 257.89199999999994 463.835 C 228.01899999999995 463.835 214.55199999999994 475.428 214.55199999999994 492.828 C 214.55199999999994 507.846 223.42299999999994 516.382 240.13599999999994 516.382 C 250.88799999999995 516.382 258.90599999999995 511.94499999999994 266.41599999999994 505.453 L 267.7819999999999 514.674 L 281.42799999999994 514.672 z M 36.847 529.099 C 31.889000000000003 536.3380000000001 23.881 542.065 15.114 545.3050000000001 L 6.527 535.2 C 13.2 531.7760000000001 18.923000000000002 526.2460000000001 21.582 521.096 C 23.882 516.515 24.834 510.611 24.834 496.492 L 24.834 399.497 L 43.312 399.497 L 43.312 495.163 C 43.311 514.038 41.802 521.663 36.846999999999994 529.099" stroke-linecap="round" />
</g>
</g>
</g>
</svg>