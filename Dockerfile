FROM node:18-alpine AS base

FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json yarn.lock* package-lock.json* pnpm-lock.yaml* ./
RUN \
  if [ -f yarn.lock ]; then yarn --frozen-lockfile; \
  elif [ -f package-lock.json ]; then npm i; \
  elif [ -f pnpm-lock.yaml ]; then corepack enable pnpm && pnpm i --frozen-lockfile; \
  else echo "Lockfile not found." && exit 1; \
  fi


FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# ENV NEXT_PUBLIC_API_BASE_URL="https://api.eremotehire.com/"
# ENV NEXT_PUBLIC_CLIENT_URL="https://dashboard.eremotehire.com/auth/login/"
# ENV NEXT_PUBLIC_CLIENT_REGISTER_URL="https://dashboard.eremotehire.com/auth/register"

ENV NEXT_PUBLIC_API_BASE_URL="https://api.eremotehire.com/"
ENV NEXT_PUBLIC_CLIENT_URL="https://perh-cp-************.us-central1.run.app/auth/login/"
ENV NEXT_PUBLIC_CLIENT_REGISTER_URL="https://perh-cp-************.us-central1.run.app/auth/register"
ENV NEXT_PUBLIC_GPT_VETTING_API_URL="https://gpt-************.us-central1.run.app/"
ENV NEXT_PUBLIC_CLIENT_URL="https://dashboard.eremotehire.com/auth/login/"

RUN \
  if [ -f yarn.lock ]; then yarn run build; \
  elif [ -f package-lock.json ]; then npm run build; \
  elif [ -f pnpm-lock.yaml ]; then corepack enable pnpm && pnpm run build; \
  else echo "Lockfile not found." && exit 1; \
  fi

FROM base AS runner
WORKDIR /app

ENV NODE_ENV=production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

RUN mkdir .next
RUN chown nextjs:nodejs .next

COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT=3000

ENV HOSTNAME="0.0.0.0"
CMD ["node", "server.js"]