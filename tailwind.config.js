/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    // Or if using `src` directory:
    "./src/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  mode: "jit",
  theme: {
    extend: {
      animation: {
        marquee: "marquee 10s linear infinite",
        marquee2: "marquee2 10s linear infinite",
        recording: "recording 1.5s ease-in-out infinite",
        'slow-spin': 'spin 8s linear infinite',
        'pulse': 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'pulse-delay': 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite 1s',
        'breathing': 'breathing 3s ease-in-out infinite',
        'breathing-delay': 'breathing 3s ease-in-out infinite 1.5s',
      },
      keyframes: {
        marquee: {
          "0%": { transform: "translateX(0%)" },
          "100%": { transform: "translateX(-100%)" },
        },
        marquee2: {
          "0%": { transform: "translateX(100%)" },
          "100%": { transform: "translateX(0%)" },
        },
        recording: {
          "0%": { transform: "scale(1)", opacity: "1" },
          "100%": { transform: "scale(1.3)", opacity: "0" },
        },
        spin: {
          '0%': { transform: 'rotate(0deg)' },
          '100%': { transform: 'rotate(360deg)' },
        },
        pulse: {
          '0%, 100%': { opacity: '0.2' },
          '50%': { opacity: '0.15' },
        },
        breathing: {
          '0%, 100%': { 
            transform: 'scale(1)',
            opacity: '0.3'
          },
          '50%': { 
            transform: 'scale(1.2)',
            opacity: '0.6'
          },
        },
      },
      backgroundImage: {
        "gradient-radial": "radial-gradient(var(--tw-gradient-stops))",
        "gradient-conic":
          "conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))",
      },
      fontFamily: {
        outfit: ["Outfit", "serif"],
        open: ["Outfit", "sans-serif"],
        opensans: ["Open Sans", "sans-serif"],
      },
      screens: {
        xs: "320px",
        xlg: "1480px",
      },

      colors: {
        white: "#FFFFFF",
        Tosca: "#8D3F42",
        Contessa: "#BC7666",
        Alto: "#D9D9D9",
        Cod_Gray: "#080808",
        Mine_Shaft: "#161616",
        contessa: "#BC7666",
        tosca: "#8D3F42",
        CodGray: "#161616",
        tundora: "#161616",
      },
    },
  },
  variants: {},
  plugins: [],
};
